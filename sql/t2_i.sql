create table warehouse (w_id int, w_name char(10), w_street_1 char(20), w_street_2 char(20), w_city char(20), w_state char(2), w_zip char(9), w_tax float, w_ytd float);
create table district (d_id int, d_w_id int, d_name char(10), d_street_1 char(20), d_street_2 char(20), d_city char(20), d_state char(2), d_zip char(9), d_tax float, d_ytd float, d_next_o_id int);
create table customer (c_id int, c_d_id int, c_w_id int, c_first char(16), c_middle char(2), c_last char(16), c_street_1 char(20), c_street_2 char(20), c_city char(20), c_state char(2), c_zip char(9), c_phone char(16), c_since char(30), c_credit char(2), c_credit_lim int, c_discount float, c_balance float, c_ytd_payment float, c_payment_cnt int, c_delivery_cnt int, c_data char(50));
create table history (h_c_id int, h_c_d_id int, h_c_w_id int, h_d_id int, h_w_id int, h_date char(19), h_amount float, h_data char(24));
create table new_orders (no_o_id int, no_d_id int, no_w_id int);
create table orders (o_id int, o_d_id int, o_w_id int, o_c_id int, o_entry_d char(19), o_carrier_id int, o_ol_cnt int, o_all_local int);
create table order_line (ol_o_id int, ol_d_id int, ol_w_id int, ol_number int, ol_i_id int, ol_supply_w_id int, ol_delivery_d char(19), ol_quantity int, ol_amount float, ol_dist_info char(24));
create table item (i_id int, i_im_id int, i_name char(24), i_price float, i_data char(50));
create table stock (s_i_id int, s_w_id int, s_quantity int, s_dist_01 char(24), s_dist_02 char(24), s_dist_03 char(24), s_dist_04 char(24), s_dist_05 char(24), s_dist_06 char(24), s_dist_07 char(24), s_dist_08 char(24), s_dist_09 char(24), s_dist_10 char(24), s_ytd float, s_order_cnt int, s_remote_cnt int, s_data char(50));
create index orders (o_w_id, o_d_id, o_id);
create index order_line (ol_w_id, ol_d_id, ol_o_id, ol_number);
create index new_orders (no_w_id, no_d_id, no_o_id);
insert into warehouse values (1, 'ZTDAt58cTb', 'gg4JAQ6tA0juxYz4K1i7', '1g3zj0duDdsSavtyhFz9', 'tWVhV7XqwnOD5hFuWAin', 'fj', 'qqoUDdkZj', 0.150000, 300000.000000);
insert into district values (1, 1, 'ecOHjIfbuc', 'UQ28P1lusnNiCt1OmgfA', 'RSr21T0D0dnILeyqgajk', '2LlJaFucV6A9bGNDMb0j', 'Va', 'kMQK8DSpR', 0.150000, 300000.000000, 11);
insert into district values (2, 1, '1EMZPMoiqR', 'scNKNiovNdvu6LzbbBd5', 'FfgwaBPDepIHHpIjFyCd', 'NfxYTWUMeQq103cnVIdO', '0T', '2xz0Mfp98', 0.140000, 300000.000000, 11);
insert into district values (3, 1, 'sYYHarrX9t', 'QIoPzM3praCl0RQFDKH1', 'ZBL2GrUuWoRcTKHaIlwN', 'AcwuDK5ivp2l169MSMmf', '1A', 'UveKlCNl3', 0.160000, 300000.000000, 11);
insert into customer values (1, 1, 1, 'P8B2f7usxISHzhHs', 'OE', 'BARRBARRBARR', 'U8xH586znr4kbj9uGDHn', 'XBEiW8awRfIhHkyvg8ae', 'lI8BViIvywskMot1s7LF', 'dX', 'WsetPirjc', '5303307448216853', '2023-06-23 10:26:17', 'GC', 50000, 0.260000, 10.000000, 10.000000, 1, 0, '7WzWGOg7uCnswEPcJTEdTXTAhMljlRS7c41mI0s26iFRpMIUXM');
insert into customer values (2, 1, 1, 'Lx5k0NG5FrHe4HKw', 'OE', 'BARRBARROUGH', 'L8QEncm6ZwfPThP2216Z', 'p8Hwxe46hZEVjiAVBUue', 'EN3WESsxMNJTYbbQwUJN', 'EE', 'hxcyY3kMr', '2151858647621530', '2023-06-23 10:26:17', 'GC', 50000, 0.110000, 10.000000, 10.000000, 1, 0, 'EWoCVvmbPBijRxCtwmhofEprQvfTNghSaaMpWurV52W8zveNoV');
insert into customer values (3, 1, 1, 'fT4tPiv92Ca2ccEX', 'OE', 'BARRBARRABLE', 'C9BPExBD18xjz4zdY4uw', 'j0Kxfmu05RqqEMBifgNC', 'EHGxBZbmCTV5Ojby1J68', 'Wo', 'kczVTj8sr', '9706861139983159', '2023-06-23 10:26:17', 'GC', 50000, 0.370000, 10.000000, 10.000000, 1, 0, 'wosuJmvtfNZNO8j0EXQOT2nwi8FkFUfmJS4fXgpGj07tqEGMg2');
insert into customer values (4, 1, 1, 'AxWnXPmrIGnRmiUz', 'OE', 'BARRBARRPRII', 'ZIzLFru8jDJdgHZTPP8m', 'rWAjKJE9BDyVrzCAnRcb', 'Wk2uHXSg2qBqpZgnEfYP', 'E8', 'TfgdujWJ8', '3888398673127275', '2023-06-23 10:26:17', 'GC', 50000, 0.200000, 10.000000, 10.000000, 1, 0, 'lKEjhAc6HTfAsqm5hAImAzQwrIUf36d3zHq5TzJ96D09SRMTC3');
insert into customer values (5, 1, 1, 'whCnG2QdxH4gafST', 'OE', 'BARRBARRPRES', 'tkLuzcfRwfytJyS0oAwj', 'U6h64zaKMZTh3hWi6mgC', 'rvpwSS0WCNr3jO7VIgyv', 'k4', 'nURpINUkZ', '5339349622401972', '2023-06-23 10:26:17', 'BC', 50000, 0.330000, 10.000000, 10.000000, 1, 0, '5oWN80vCu7UuItVG0z7t5evD0ksA5ANpGVDdyFMEte6OXqX27T');
insert into customer values (6, 1, 1, 'v4BjwaKnzgEGNlaQ', 'OE', 'BARRBARRESEE', 'LAUACbEdJfvZF7Bb5GOX', '5QvRuViww14aWDZDNnH8', 'tBx2tMCnH0R4vy5lNLsS', 'IG', 'xw192Ro1l', '1809254868070200', '2023-06-23 10:26:17', 'GC', 50000, 0.100000, 10.000000, 10.000000, 1, 0, 'xxgncSK7LHln0SsY6o1nDTPy5qiA3CdffNdZOQOrjFX3L9tGwo');
insert into customer values (7, 1, 1, 'ebYA039DhGoJlouG', 'OE', 'BARRBARRANTI', 'hLtXEaFIy7fxVFcmODDp', 'xy3B6BPeAipQ4g5VJiPK', '1VDFPIKu06LgEtLiFE1q', 'zY', 'afvn3bDN0', '4434734611211247', '2023-06-23 10:26:17', 'GC', 50000, 0.080000, 10.000000, 10.000000, 1, 0, 'Qk9Qhd6xcuk1ezE2Uo5S8cdl6TngncJThMQkmXDwopzcbWdIIg');
insert into customer values (8, 1, 1, 'W6pShvL4LUgpLyrw', 'OE', 'BARRBARRCALL', '7ipV4oDtz7RVDdOg5RoF', 'UKRezIV4cu0gM39QpSfv', 'S1zwFacYbpaZENPsKkfj', 'B0', 'EIMalh7dP', '8834260908910437', '2023-06-23 10:26:17', 'GC', 50000, 0.450000, 10.000000, 10.000000, 1, 0, 'ffu2S9S5Tpx2hPWtSZA5wwXhcKPAp2vnX7YNrynBQz5fi2YysZ');
insert into customer values (9, 1, 1, 'IFxYMQBuoWim7GLO', 'OE', 'BARRBARRATIO', 'IkLwhTiI3OEMKqLixHxg', '0R1vZxwIA07eXWbAhRPV', 'shvUV2Jj1vA8zsBvIng3', 'rY', '6BGoJ3lCm', '7444866366188846', '2023-06-23 10:26:17', 'GC', 50000, 0.320000, 10.000000, 10.000000, 1, 0, 'dS9My0CwtmZGoG5qcryCDMkyBEL0ZXambvtPf25P1zMYMkqlbC');
insert into customer values (10, 1, 1, '59DdhOUsH8CpWFgl', 'OE', 'BARRBARREING', 'IFhydN6iMKSJT9s8WtqJ', 'w4Hf6ZgunscJkGFmnXYK', 'Jtgzc4xc4YHMqmKtpVwd', 'au', 'CQVyyL2ZB', '5343262499512687', '2023-06-23 10:26:17', 'BC', 50000, 0.000000, 10.000000, 10.000000, 1, 0, 'RPlUqGacqlDQ1mfK7Z9oIhQhxf5bAlm1t5MBSxG9Id67iuk7Ca');
insert into customer values (1, 2, 1, 'QiRRhmo1tFuOFGX2', 'OE', 'BARRBARRBARR', 'JH82anPyh2Z886EHVoBI', 'wW3EcDTzZgp3sDLSbJXL', 'bdVXyMn8qBitWZpGX7Zz', 'bx', 'TRccx7T9q', '0104367083119091', '2023-06-23 10:26:17', 'BC', 50000, 0.430000, 10.000000, 10.000000, 1, 0, 'OgHgKpg8cDanPc5EtAmwX90aHLmAoI90Zsoc6b1LPrPioGCdNT');
insert into customer values (2, 2, 1, 'VKST00WK33DVdOTu', 'OE', 'BARRBARROUGH', 'fHemrcN5Z16XVufK1dNZ', 'hLZu87DIUn4MiXQoWwIa', 'wsB53yt7Sh68ndv14sUd', '3F', 'roVxJ96qU', '4930315919569742', '2023-06-23 10:26:17', 'GC', 50000, 0.010000, 10.000000, 10.000000, 1, 0, 'V5Zc3nDSP4vtePtyN910Zgu20hVt6GwQWNNsMULluinBBRZ30t');
insert into customer values (3, 2, 1, 'AlPeA6WdvrWPn0l6', 'OE', 'BARRBARRABLE', 'MbJsRFu8hLhQdDfPZwQO', 'zfNPyHo0v1COzCP3Zp8q', 'BRWrjTmQhL0rOOLU9rqB', '4b', 'MWnjpeSxA', '6803482204323237', '2023-06-23 10:26:17', 'BC', 50000, 0.180000, 10.000000, 10.000000, 1, 0, 'nqtuomEoYvWMyeWmGgyPikYXtXtX9YO9XZDTeOcRfLkAhw8TFQ');
insert into customer values (4, 2, 1, 'JgpbDg0jvpnvwT0W', 'OE', 'BARRBARRPRII', 'ReRKeUFy5NKO4bC89uYa', '7zP3cWNCpQN1a4GV0jbs', '1jFxe6R3a1R1j6QnheoO', 'pi', 'GEnwXS50u', '2590983985048425', '2023-06-23 10:26:17', 'BC', 50000, 0.000000, 10.000000, 10.000000, 1, 0, 'NgKkhCsuBgECwwlsMFjlrDBJ8qF5PfSmLEiDNHmarLRDoEueUN');
insert into customer values (5, 2, 1, 'ZgLGVIWls6RorBha', 'OE', 'BARRBARRPRES', 'h5tLSGYNMS8XQlBaiH1k', 'XP9glmkKVSZbD2ZIUoRU', 'FV2rq8TIy4wlnpg2rjU5', 'xR', 's6SgBMFhI', '5304529026055135', '2023-06-23 10:26:17', 'GC', 50000, 0.380000, 10.000000, 10.000000, 1, 0, 'GOrs9E41EcfeR2ygbJTt17RTpvlkMnrhUD4mVAiRVRCAmOifwV');
insert into customer values (6, 2, 1, 'ky6lmSAdMCtYKjxA', 'OE', 'BARRBARRESEE', 'goZfIxjKE3gPoXLS3jD0', 'm7KAurZWHyHEvciKjg8R', 'XpBGr2zhDyqiDQlDiDgj', 'UE', '8EfZjPcUd', '5041118580584052', '2023-06-23 10:26:17', 'BC', 50000, 0.140000, 10.000000, 10.000000, 1, 0, 'sLnnsjXiA2pqzE1RaYVvBXnznXSRsA9JcdEioYgaIhwKoYxeZt');
insert into customer values (7, 2, 1, 'VXb2WAeURESmkkt0', 'OE', 'BARRBARRANTI', 'w0OqpEnK1Gpw6gGEnWL6', 'Ar3sUL71FWVt0MIkcGdv', '4yze8NMHBKbKa1ooV8ae', 'yN', 'zZf4tEV2q', '7672565282150878', '2023-06-23 10:26:17', 'GC', 50000, 0.370000, 10.000000, 10.000000, 1, 0, 'nZoO0AKTe8iKKDxgJIdwtPn7WVC9W8NV1lZ3uaVMtgaE7xZFz1');
insert into customer values (8, 2, 1, 'iFe5twy7J2a4JUie', 'OE', 'BARRBARRCALL', 'rKAxvfyHfLovPj4lbX2t', '08YBUnvBnxWSkvMSKNly', 'ADRFkRMUPAcxTqbBFzXs', 'cy', 'kDw8AQ4hT', '2962867350506989', '2023-06-23 10:26:17', 'GC', 50000, 0.250000, 10.000000, 10.000000, 1, 0, 'NWVQ1vlcKrJG0rx8ZORvirm54VlVCilP6juj3T5S2GTH0kJQCf');
insert into customer values (9, 2, 1, '1OBbg2isHFzhDEOC', 'OE', 'BARRBARRATIO', 'QA9GeQXJBlc0FvmZq2mm', 'OVFAGDqmQrNS44V2NYB4', 'hysgUlhO7RduYDRZE0z3', '6R', 'Pki0dclPE', '5719778274079954', '2023-06-23 10:26:17', 'BC', 50000, 0.440000, 10.000000, 10.000000, 1, 0, 'MEIMxZELbCg8AEOBcQkm8cf6pKBZyWf88Q3oyCuLvBMxTBH733');
insert into customer values (10, 2, 1, 'AkEO5wWVuKKn0Ug4', 'OE', 'BARRBARREING', 'xJ6fc87hfgioUXCLiKqP', 'ghemhDEdIps6vScm3pYK', 'bZupXz35suGpmfeJvsfV', 's0', 'ycMIBn07e', '0308631558281867', '2023-06-23 10:26:17', 'BC', 50000, 0.240000, 10.000000, 10.000000, 1, 0, 'BdrKsRTIwNqAnFc8TsglGoA3Ow6bKfF8oa4O5k4rlGVjuG8zlS');
insert into customer values (1, 3, 1, 'TKZX1fAhfgNB0N2o', 'OE', 'BARRBARRBARR', 'LgKgHXsjrcCIUlyoDHXJ', '24WOm8K8co6S9YgfcjtJ', 'WxfCkFNbRYX1TJLyH0A2', 'M5', 'NuWDIBRhL', '7185638665079438', '2023-06-23 10:26:17', 'GC', 50000, 0.300000, 10.000000, 10.000000, 1, 0, 'Ywnr7Z8qdA9SfrArw7F0BdRD2NfYMuKwLVcsG503YYwhqlAeJP');
insert into customer values (2, 3, 1, 'KeA1zjSHz9zxazHs', 'OE', 'BARRBARROUGH', '1ntsS1NsvjC3vYdpsREJ', 'rJOVRxnua3LzlQCpPJp7', 'wuNY5kjHzBTE4fcWDbmY', 'PH', '0Mw9fnmBr', '1715023489594693', '2023-06-23 10:26:17', 'GC', 50000, 0.370000, 10.000000, 10.000000, 1, 0, 'Hgqy5W4xDSblWF4aISqRLtJD5xqhjkxvZpljWYtif1YzUmJfl1');
insert into customer values (3, 3, 1, 'lCWudAvS0KkIJuBO', 'OE', 'BARRBARRABLE', 'xVHZzqCYdfyiZutoEU3G', '6UitUak2rVxYcX2XDcO1', 'YyXiJZMZ2IdMcvz6HBTy', 'Ca', '54XBPAqUO', '9539019106339186', '2023-06-23 10:26:17', 'GC', 50000, 0.480000, 10.000000, 10.000000, 1, 0, 'i4cg0KWUmOsYq5TTnTCqXb0Vhxzeo5QrnsGr1PicaFrfxeNUVT');
insert into customer values (4, 3, 1, '8u5to5Yu3g9Yqkf4', 'OE', 'BARRBARRPRII', '4MAx7VCRyhkK8cskS1yG', '2zkYh1jqePEOVqHHPDCO', 'IeEcsD724ko8pqpFFxc8', '9L', 'XvRtpSxdg', '9436473904793356', '2023-06-23 10:26:17', 'GC', 50000, 0.040000, 10.000000, 10.000000, 1, 0, 'vqW1PuD5V9VdeN0cEnip9unDw8o1VBQPOrIEZvyAqz3zwhCYnY');
insert into customer values (5, 3, 1, 'TCrItoyNtouqcNuw', 'OE', 'BARRBARRPRES', 'YeHymyeHpoboIuMyMBLF', 'hi0MWyAsnJvidNZKC6hg', 'zzYmkyV0Mxs02oI2onjK', 'tG', '88RAljqbI', '8670901432637902', '2023-06-23 10:26:17', 'GC', 50000, 0.310000, 10.000000, 10.000000, 1, 0, 'nCcAOukZFURXSxJrK0Vg3YUhn7StLAgJaSTzTDMtwGxMlE2fxq');
insert into customer values (6, 3, 1, 'ncOiBx3eB16Hzm6r', 'OE', 'BARRBARRESEE', 'Q0XWmCgH9oYMYYnhHzhQ', 'wIzuBC7gGT31xAsKGdPt', 'UioGQtJ2Fh1g84yZdeCu', 'ra', 'PboAP0pJ1', '7451972227902502', '2023-06-23 10:26:17', 'BC', 50000, 0.410000, 10.000000, 10.000000, 1, 0, 'kWTwa9yygUeF4dZIvQyreG6H8QRt7tWYv8f8mXiHo9EuCAHrXc');
insert into customer values (7, 3, 1, 'W4n0aMxfXqPWfuJd', 'OE', 'BARRBARRANTI', 'u4szjaVOckg1hkPJaI2p', 'tQ3eO8RWH4nvU3n7BLzV', 'I3NajM7anH2jnwTUVV3k', 'bL', '9kldJcqqX', '4869352808069570', '2023-06-23 10:26:17', 'BC', 50000, 0.020000, 10.000000, 10.000000, 1, 0, '0ndnjYGEOBwiwbB2BU5PsULFitHj5TuXQcoWz5Y4LYhGDYv3L2');
insert into customer values (8, 3, 1, '5AuNtuf7rooQJzhh', 'OE', 'BARRBARRCALL', 'oc32pUrM3MZF9HRG1P1J', 'ExuMeS53RCnu6IlyFExM', '1T5xDlHmxFixtl25OpwC', 'Xs', 'cAVJuhtIw', '4016284299254609', '2023-06-23 10:26:17', 'GC', 50000, 0.260000, 10.000000, 10.000000, 1, 0, 'kfCwpOGKUMTUdAH5SuthV4t2GeSj5ASh3VjazOgQk54u0T4e9W');
insert into customer values (9, 3, 1, 'Y8f46WT2eCxpQL2S', 'OE', 'BARRBARRATIO', '79kY57vGAht2eTrwl0t9', '1HfeyU0JghTmh4oh2qLk', 'rL2vWyxljC6nkMxjC1kQ', 'f7', 'Jov1DNMJB', '5082836691468296', '2023-06-23 10:26:17', 'GC', 50000, 0.000000, 10.000000, 10.000000, 1, 0, '5StOs9JgwGN1mf0TsSF4cMCTh6V2XFf6fj27uYFUJ6g2zjnJH5');
insert into customer values (10, 3, 1, '0w8Xdjj3ZjFlzrAV', 'OE', 'BARRBARREING', 'p5dUCGDNrbnJhHaZ5AF7', 'Cj3AKIUEfYynOpI8kYUO', 'L3fj3HqU9NBKCt6fmBW9', 'LB', 'tLgCoWVCq', '2068944149321334', '2023-06-23 10:26:17', 'GC', 50000, 0.360000, 10.000000, 10.000000, 1, 0, 'i15NNvRqu70LSz1eYMO3rByBIZUSpOiDgPidgFUYv5u3xwkDn8');
insert into history values (1, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'zF9peM9czrCaI6hswNQ6EaUA');
insert into history values (2, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'ZGhE2woDIPhQpakXZijgvKu7');
insert into history values (3, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'v0iZe7f9llfHzSnr1hTXxlyw');
insert into history values (4, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'HTkk0Chdzos5XcDQcyreFqvj');
insert into history values (5, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'QX9ciyb9mSUPmOycE3BwGlbK');
insert into history values (6, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'QGOOxHdPbDz6whCAye0VA68N');
insert into history values (7, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'LfEclOnvDWD9KA0JCT7IilQi');
insert into history values (8, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'uVKDIapt3sYQuHvnIOTDmyak');
insert into history values (9, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'HG7hFXqbfjnI9VCFqZ0xFsvp');
insert into history values (10, 1, 1, 1, 1, '2023-06-23 10:26:17', 10.000000, 'InTtgeBPIlRvOUqaA6c7ADlF');
insert into history values (1, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'IIWjxclD4VkCKkQvWKDNBDUN');
insert into history values (2, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, '1HmZoX0hiVXLtDjIvPIDg4Ou');
insert into history values (3, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'wUzseXEUxdJB24T9ve6ji2ya');
insert into history values (4, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'rX9KCMahuYQCf6HNSjtHsNgH');
insert into history values (5, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'd73QD6wX63x5A2apLRJOOXvH');
insert into history values (6, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'JbDYd79xzlE6YfZreOygS2Vw');
insert into history values (7, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'JLGQMI3zpm3kUpVD1G5ikMWF');
insert into history values (8, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'fSttHT52t0M34Awm5YEWxld8');
insert into history values (9, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, 'OT0VJ8U8gYRazNwKMSyKWpQa');
insert into history values (10, 2, 1, 2, 1, '2023-06-23 10:26:17', 10.000000, '0LN25n8jOGxjdADs1gYBuOQF');
insert into history values (1, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'UeUOc0ygJvdPOkdNcsgIdeSq');
insert into history values (2, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'YSo1H6mSCFctMBAO6ECtFN8X');
insert into history values (3, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'ba5vzOwKmzsT1wXnalNlusz0');
insert into history values (4, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'nQRe6jvCtwLyeOTZ6wTFB0di');
insert into history values (5, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'qVOmQsJv3g2MaTibUlOG8UCW');
insert into history values (6, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'aSpOl6dWM6xE0uuyZGI1X4bO');
insert into history values (7, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'owqPnFXKUqE1I25bcCjPRxSz');
insert into history values (8, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'oQ3q0Sc4DtigufEELsfK3g2U');
insert into history values (9, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'yKUOXM5ifXZKBxGjRPtY958s');
insert into history values (10, 3, 1, 3, 1, '2023-06-23 10:26:17', 10.000000, 'j6NKbAg3IYaES9FnMUy1JfSp');
insert into new_orders values (5, 1, 1);
insert into new_orders values (6, 1, 1);
insert into new_orders values (7, 1, 1);
insert into new_orders values (8, 1, 1);
insert into new_orders values (9, 1, 1);
insert into new_orders values (10, 1, 1);
insert into new_orders values (5, 2, 1);
insert into new_orders values (6, 2, 1);
insert into new_orders values (7, 2, 1);
insert into new_orders values (8, 2, 1);
insert into new_orders values (9, 2, 1);
insert into new_orders values (10, 2, 1);
insert into new_orders values (5, 3, 1);
insert into new_orders values (6, 3, 1);
insert into new_orders values (7, 3, 1);
insert into new_orders values (8, 3, 1);
insert into new_orders values (9, 3, 1);
insert into new_orders values (10, 3, 1);
insert into orders values (1, 1, 1, 2, '2023-06-23 10:26:17', 7, 7, 1);
insert into orders values (2, 1, 1, 8, '2023-06-23 10:26:17', 6, 12, 1);
insert into orders values (3, 1, 1, 10, '2023-06-23 10:26:17', 2, 13, 1);
insert into orders values (4, 1, 1, 9, '2023-06-23 10:26:17', 5, 7, 1);
insert into orders values (5, 1, 1, 6, '2023-06-23 10:26:17', 0, 15, 1);
insert into orders values (6, 1, 1, 4, '2023-06-23 10:26:17', 0, 9, 1);
insert into orders values (7, 1, 1, 1, '2023-06-23 10:26:17', 0, 11, 1);
insert into orders values (8, 1, 1, 7, '2023-06-23 10:26:17', 0, 10, 1);
insert into orders values (9, 1, 1, 3, '2023-06-23 10:26:17', 0, 9, 1);
insert into orders values (10, 1, 1, 5, '2023-06-23 10:26:17', 0, 8, 1);
insert into orders values (1, 2, 1, 8, '2023-06-23 10:26:17', 3, 7, 1);
insert into orders values (2, 2, 1, 2, '2023-06-23 10:26:17', 7, 11, 1);
insert into orders values (3, 2, 1, 3, '2023-06-23 10:26:17', 1, 13, 1);
insert into orders values (4, 2, 1, 7, '2023-06-23 10:26:17', 3, 5, 1);
insert into orders values (5, 2, 1, 10, '2023-06-23 10:26:17', 0, 14, 1);
insert into orders values (6, 2, 1, 4, '2023-06-23 10:26:17', 0, 10, 1);
insert into orders values (7, 2, 1, 5, '2023-06-23 10:26:17', 0, 8, 1);
insert into orders values (8, 2, 1, 9, '2023-06-23 10:26:17', 0, 9, 1);
insert into orders values (9, 2, 1, 6, '2023-06-23 10:26:17', 0, 15, 1);
insert into orders values (10, 2, 1, 1, '2023-06-23 10:26:17', 0, 6, 1);
insert into orders values (1, 3, 1, 10, '2023-06-23 10:26:17', 8, 13, 1);
insert into orders values (2, 3, 1, 8, '2023-06-23 10:26:17', 8, 15, 1);
insert into orders values (3, 3, 1, 6, '2023-06-23 10:26:17', 6, 9, 1);
insert into orders values (4, 3, 1, 5, '2023-06-23 10:26:17', 7, 15, 1);
insert into orders values (5, 3, 1, 2, '2023-06-23 10:26:17', 0, 8, 1);
insert into orders values (6, 3, 1, 1, '2023-06-23 10:26:17', 0, 12, 1);
insert into orders values (7, 3, 1, 9, '2023-06-23 10:26:17', 0, 8, 1);
insert into orders values (8, 3, 1, 3, '2023-06-23 10:26:17', 0, 13, 1);
insert into orders values (9, 3, 1, 4, '2023-06-23 10:26:17', 0, 10, 1);
insert into orders values (10, 3, 1, 7, '2023-06-23 10:26:17', 0, 9, 1);
insert into order_line values (1, 1, 1, 1, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'm05Mu9IGeQls7ZKmfkQql9ta');
insert into order_line values (1, 1, 1, 2, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Msfb894UNsr8YFbRIUSDUs7K');
insert into order_line values (1, 1, 1, 3, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'DmuxywpyGL3pv2YtcAdJH7qT');
insert into order_line values (1, 1, 1, 4, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'YXwbQQI9CYGeHUzJRJeE49OB');
insert into order_line values (1, 1, 1, 5, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'zz6b04C707hSniUgbuCXpB1C');
insert into order_line values (2, 1, 1, 1, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, '0a58lsUb0EvxQhiT1lKdZsDh');
insert into order_line values (2, 1, 1, 2, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'aPjJho3KeEQdxZEWqjTLIuAh');
insert into order_line values (2, 1, 1, 3, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'UMiB6KYTvwJwkuJ1QxpNwKJY');
insert into order_line values (2, 1, 1, 4, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'rQ5B95nSC9SCfuZxKtkLjXJe');
insert into order_line values (2, 1, 1, 5, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ngzsZO4agaeEvVahgXNZC8wA');
insert into order_line values (2, 1, 1, 6, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'CRRg860eBQU2ONuhdBzkDI1m');
insert into order_line values (2, 1, 1, 7, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mLyZtD5owlLMrgGnAL5z1bqY');
insert into order_line values (2, 1, 1, 8, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'OCOVIjIMxqu7WfBMbpncX3QP');
insert into order_line values (3, 1, 1, 1, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'B3fj1QJv8HejibVjTlFk87SW');
insert into order_line values (3, 1, 1, 2, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ny3ZL2R4x4jKkkwRKxJGobWe');
insert into order_line values (3, 1, 1, 3, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'C4YcYGaaeQdWd3IRzlGkR0MM');
insert into order_line values (3, 1, 1, 4, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'gQ8LoSc2771uLi2E1L5lUco4');
insert into order_line values (3, 1, 1, 5, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'g8o4fC2asOKyb6tjjyfmCtBy');
insert into order_line values (3, 1, 1, 6, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'yO7qQGqeX6xB0nv0uW0N9fFc');
insert into order_line values (3, 1, 1, 7, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'TwqAK274S43OE7AbuY0VX9nR');
insert into order_line values (3, 1, 1, 8, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, '1V0FI1dtZFwG2eqiICTYIZDp');
insert into order_line values (3, 1, 1, 9, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'v9odNcEY8bwsUYCacPQtMReg');
insert into order_line values (3, 1, 1, 10, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'A6wkk6YukUPXxcYNim6q2cRy');
insert into order_line values (3, 1, 1, 11, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'MEUt1hS14FifVOYkfCIoidjG');
insert into order_line values (4, 1, 1, 1, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'VCom52UCYQ5hL8VQzaprnWK1');
insert into order_line values (4, 1, 1, 2, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ZAeCbbfUsEjKaIaM5v0J4qky');
insert into order_line values (4, 1, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'GSWh4z2hMPNK96hihPymZgF4');
insert into order_line values (4, 1, 1, 4, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'YJPqLEyIJlWzNOnj0HsN6l8a');
insert into order_line values (4, 1, 1, 5, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, '6Egt3UyCl7x9k8mHXtnX5bHm');
insert into order_line values (4, 1, 1, 6, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'TAWKjmNLOpY95ay2UsH06T4G');
insert into order_line values (4, 1, 1, 7, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hLcoX2i1Iha63gCCAot1WIE7');
insert into order_line values (4, 1, 1, 8, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '1eThP7EVsEJs0Y6kGThi2tCP');
insert into order_line values (4, 1, 1, 9, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'EjKscU2PUGgZXaa59fblgSLS');
insert into order_line values (4, 1, 1, 10, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'u79jV8J5PQSfEq8C6526PzGC');
insert into order_line values (4, 1, 1, 11, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, '4iqcL8VYBGoepR5GtVAKepCN');
insert into order_line values (4, 1, 1, 12, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'TGCA0s7kvM1JETG7xywG4qO6');
insert into order_line values (4, 1, 1, 13, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'dWEwM3mn1aQIM9quZp7up42d');
insert into order_line values (5, 1, 1, 1, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mZ4OH9Bb4xkegTRdB80nyblX');
insert into order_line values (5, 1, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'VKBT1V9RvlSfKoufCy22PFwi');
insert into order_line values (5, 1, 1, 3, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LOrBfanXEvv030XpyJdkdIAZ');
insert into order_line values (5, 1, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'm7pZhCkICReEzHBk26byikkN');
insert into order_line values (5, 1, 1, 5, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Pc3uBRwANLCzcDLYYq9pIdDL');
insert into order_line values (5, 1, 1, 6, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'UMQHLk4q9nwpihnMs3EE1hlv');
insert into order_line values (5, 1, 1, 7, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'tGIjnpui8lAfdFRgyo6kHcky');
insert into order_line values (6, 1, 1, 1, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'b1DEUdT6UhyR33Nil6ezdqXA');
insert into order_line values (6, 1, 1, 2, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'lvQQQGSVBajrOcar4x2bFFIq');
insert into order_line values (6, 1, 1, 3, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '0Nvgg0KwH4pUOkVD77YW02eL');
insert into order_line values (6, 1, 1, 4, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'bMpvhlqxK9q4ppOEnAbF09Hr');
insert into order_line values (6, 1, 1, 5, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'oZSVPjoSiFeqZdk0BBHxDndV');
insert into order_line values (6, 1, 1, 6, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '8PxZAJIIDAVRsBnMO3dtt5RA');
insert into order_line values (6, 1, 1, 7, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'heYKlfsVYOvGu7SDiUwR1LZQ');
insert into order_line values (6, 1, 1, 8, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'DRz2oxfrlp9Zqk37CgkYwClq');
insert into order_line values (6, 1, 1, 9, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'vqk09ru4mK631OutxQZYh9V2');
insert into order_line values (6, 1, 1, 10, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, '15YgrmeH99ueHYDEvLSyPMNb');
insert into order_line values (6, 1, 1, 11, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, '7BGw5gGgQNd7hGD9weScKYYh');
insert into order_line values (6, 1, 1, 12, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '0iEqnrUqEr6JJggrULQ0JKZf');
insert into order_line values (6, 1, 1, 13, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'i4ZoC6st4Gg8f6CwCMjKD4NL');
insert into order_line values (6, 1, 1, 14, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'clHpxE5oGkAp6LStmdveHMjf');
insert into order_line values (6, 1, 1, 15, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'nGnKE494oBEGARP0tN6x5r5f');
insert into order_line values (7, 1, 1, 1, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JZOqZcWhEQuJ8tclsoprGF9t');
insert into order_line values (7, 1, 1, 2, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, '8LMgzmzNGyZ6X1MmMjMmjqoZ');
insert into order_line values (7, 1, 1, 3, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'C0TsYDxDHV8mEU2MSeyfrydt');
insert into order_line values (7, 1, 1, 4, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'CswZ3nDqTEjU6kSUx1d5z7XY');
insert into order_line values (7, 1, 1, 5, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '6zcAosppL3JyzoIYv8f4Ab3l');
insert into order_line values (7, 1, 1, 6, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'wQg9BTy1fdGJNCtmhrhWASh7');
insert into order_line values (7, 1, 1, 7, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'K9pBe4OqT9ukvBfI7F05es8v');
insert into order_line values (7, 1, 1, 8, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mVqg9rIeGV2Gsuo4tckmGalz');
insert into order_line values (7, 1, 1, 9, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'VGdGNcm0E3EDwI6sfX95GThc');
insert into order_line values (7, 1, 1, 10, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'uIeIXFyxdSOPd9TfSYM7qqMb');
insert into order_line values (7, 1, 1, 11, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, '4HFhMVfhneJBhKFlso79DZgA');
insert into order_line values (7, 1, 1, 12, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'KtdBmrk5dKLE8wzld92zxmmA');
insert into order_line values (8, 1, 1, 1, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, '496bN8RasQyaKh2wQLgKiZiH');
insert into order_line values (8, 1, 1, 2, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ygCw4pDRDeyN46eCo4zbN475');
insert into order_line values (8, 1, 1, 3, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'HmZq2DYYrhRYlkmE71JE1niK');
insert into order_line values (8, 1, 1, 4, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Xg9RWUYa1TYbhtADJyQSE55Y');
insert into order_line values (8, 1, 1, 5, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'xRxmYJA5chF6R2mMvSPorqoQ');
insert into order_line values (8, 1, 1, 6, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ToH4omzFNsYz1S18YvjzKC07');
insert into order_line values (8, 1, 1, 7, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, '5sHVTvhIGwFK9Q0ftLiSrvM0');
insert into order_line values (9, 1, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'UwyTJdX0MfohYmuMzfuA2iCQ');
insert into order_line values (9, 1, 1, 2, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'WCYwgFtBt5WSbCe6SdXJ7BVM');
insert into order_line values (9, 1, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'P6GhQzixBd7PPlZHrkoucxdC');
insert into order_line values (9, 1, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'cgwKMVc6rjUpOKx8DcSvAEGS');
insert into order_line values (9, 1, 1, 5, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'sLLBMSD3dC2yuVwioJRXA16t');
insert into order_line values (9, 1, 1, 6, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'jaBFCQL4E9ZQA8geElkhbrWw');
insert into order_line values (9, 1, 1, 7, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'N9GsQBLJS8BXZPCNbeRyAJri');
insert into order_line values (10, 1, 1, 1, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '6mY0PJbypdRd9BQehPVi4Wl2');
insert into order_line values (10, 1, 1, 2, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'eyZJC2ru1PX4bDVSUl6AsyNe');
insert into order_line values (10, 1, 1, 3, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'jdiCOET3jPcFZah2YFH5XE6y');
insert into order_line values (10, 1, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'HTUi4hdCWcsTdMMxVwSEHvU0');
insert into order_line values (10, 1, 1, 5, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'VMaoF1BBdFvOceYNFO0spUrt');
insert into order_line values (10, 1, 1, 6, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'U263oCPczObbIUwAHmjqYadU');
insert into order_line values (10, 1, 1, 7, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'sySG4UoWgFmhiLpaxqB251V5');
insert into order_line values (10, 1, 1, 8, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '9OpM8i4a8jODMcq9LPfYnlw0');
insert into order_line values (10, 1, 1, 9, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '4wt9oPq4seWy3P3eNw4Yxzvn');
insert into order_line values (10, 1, 1, 10, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'J18KV7Bv1c5wQ9tOj9aHBlzP');
insert into order_line values (10, 1, 1, 11, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, '4NnDhHbS0GRwx0pBUMf0mSNX');
insert into order_line values (10, 1, 1, 12, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kZNWX5n8DcYV9Wzo7VcypJyc');
insert into order_line values (10, 1, 1, 13, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'oIhHb7rB14PhAZs0RWc7yigX');
insert into order_line values (10, 1, 1, 14, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'V33enneUZka0HjdL4hOV8i31');
insert into order_line values (10, 1, 1, 15, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ElP7hGULHEO6DYzNhXKzsQsh');
insert into order_line values (1, 2, 1, 1, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'O3FMOYw5lQY3a0lGSvyhAOBQ');
insert into order_line values (1, 2, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Qyrhh3h1kiA6axo0bk8HdF1x');
insert into order_line values (1, 2, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'zV34YFoXOTvfbJskpAbjSOCa');
insert into order_line values (1, 2, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, '5N3Va4BYVCl2OfhPHLFHiNd3');
insert into order_line values (1, 2, 1, 5, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ZZNJ2piE5SqEuNWZF4Nyhj6Q');
insert into order_line values (1, 2, 1, 6, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LyhNe1iCJR9ndZ5ghRnTXXaT');
insert into order_line values (1, 2, 1, 7, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JbobSR21jXBtY8eWXkKwkX8t');
insert into order_line values (1, 2, 1, 8, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'GwaZYssupVhKExxXR7FLegZ7');
insert into order_line values (1, 2, 1, 9, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, '1eS5Ps8kI7Z7XQBmPZP5gqdZ');
insert into order_line values (1, 2, 1, 10, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'RYmCFuHatyTJ9neSe2EBfi0c');
insert into order_line values (1, 2, 1, 11, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'b2uaqne5fbhFYR2lzCyAPQba');
insert into order_line values (1, 2, 1, 12, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, '8cAYd5V7vptp5SugRNLpbUGu');
insert into order_line values (1, 2, 1, 13, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'xI8PSdtupFi8BdEK29RmQrKz');
insert into order_line values (1, 2, 1, 14, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'OJwkAOBTZZT5BENVsKDaum6T');
insert into order_line values (2, 2, 1, 1, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '1yEMgZsPcFfHcxOCYf7WOklB');
insert into order_line values (2, 2, 1, 2, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mgae1VCC76alntf3aXGQrdux');
insert into order_line values (2, 2, 1, 3, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JdWCB6ejnEVwunIai3hklFeJ');
insert into order_line values (2, 2, 1, 4, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'brpOLlluPWbkKNp64lxaqw5b');
insert into order_line values (2, 2, 1, 5, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '2X1V3WxylTBuQZrSDipTop1G');
insert into order_line values (2, 2, 1, 6, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LYHZPIJJyargfRrkV2nFdz2d');
insert into order_line values (2, 2, 1, 7, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'I4ThztoxpZvkzLRUEHOtW79f');
insert into order_line values (2, 2, 1, 8, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mOm2bMQ4hGM7Vqvw3ZduCPLK');
insert into order_line values (2, 2, 1, 9, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'rnAfhuVkEHaKh9sPu0lQGUIJ');
insert into order_line values (2, 2, 1, 10, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kDvmWpkUYJlj64VxK9CVEiaj');
insert into order_line values (2, 2, 1, 11, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '4avaOqCYD3WMJNl5gIGrOBFJ');
insert into order_line values (3, 2, 1, 1, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'FktpwR3LYpYlr2Z6uoJU4p5l');
insert into order_line values (3, 2, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ORvQdXHM69h0qbnrGanqUKXd');
insert into order_line values (3, 2, 1, 3, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'AjX5ow8QpnokyJObie2kFlVs');
insert into order_line values (3, 2, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'bNMhaRMzQ3Wz3SzHoRAFKgv5');
insert into order_line values (3, 2, 1, 5, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'v726BaPdL7IyAj66kMW47aEH');
insert into order_line values (3, 2, 1, 6, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'o3IQkHnYJJqqU00W4qaaOvtQ');
insert into order_line values (3, 2, 1, 7, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'dRY9g99HHp5XnuxcMxfjJz0R');
insert into order_line values (3, 2, 1, 8, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'RA8EIXrDnHrfQu5bQkTsuquu');
insert into order_line values (3, 2, 1, 9, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kU2DjlzUpjTucd8Zp1uItvk9');
insert into order_line values (4, 2, 1, 1, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ir3JRn78Zwif2zY3c3BT24i0');
insert into order_line values (4, 2, 1, 2, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, '67oAZWKfdWJeNPB0MQpbtRDC');
insert into order_line values (4, 2, 1, 3, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '4qFKJcSNg7iaH4UGL72HoKIV');
insert into order_line values (4, 2, 1, 4, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '5rhtj0XrGKlECqQfeYxRSvpQ');
insert into order_line values (4, 2, 1, 5, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'GxxvfMgfXiz50RVYy1q9BD1f');
insert into order_line values (4, 2, 1, 6, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, '3iEsIdGdi6RjR0i34oqW1Lwx');
insert into order_line values (4, 2, 1, 7, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kZOpB1vzm40iJuG84UAKWp9I');
insert into order_line values (4, 2, 1, 8, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'fuxhNoBvdWQWcE4Hk1qJOYRo');
insert into order_line values (4, 2, 1, 9, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mco9pGH6dzQHN2hs8qTZBEYA');
insert into order_line values (4, 2, 1, 10, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'VBoQaL9i2w2jjjOlVQ48dbOD');
insert into order_line values (4, 2, 1, 11, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'YERhFM4PGJeTsNUlou9pTwzC');
insert into order_line values (4, 2, 1, 12, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'zNbNGaBvxduphkVU1VR7tKC2');
insert into order_line values (4, 2, 1, 13, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'vU9o8quRauCHScEvOCHTi0Nm');
insert into order_line values (5, 2, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'I9vVzzFPWopvVULMHAQ450VI');
insert into order_line values (5, 2, 1, 2, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'GSclle4JFRS9pPKU4zQstuqG');
insert into order_line values (5, 2, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'DZ7sWSMVx69w7WeFDL2PZZfA');
insert into order_line values (5, 2, 1, 4, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'vFWaqvOtihFjo3fg5Jl6FCC8');
insert into order_line values (5, 2, 1, 5, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'nQ6xZCeEV5RwjquYjNX7dnRo');
insert into order_line values (5, 2, 1, 6, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hFthK49sR8WZyuVnblNGEs74');
insert into order_line values (5, 2, 1, 7, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'wt6EKBa7CHOLm1UFhlUpFoP1');
insert into order_line values (5, 2, 1, 8, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'tibmzTwSlG8iAu2HHrL0oU7v');
insert into order_line values (5, 2, 1, 9, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ZTzO2R0tjrv75nQiVEVYjbuG');
insert into order_line values (5, 2, 1, 10, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'BFyC4aRAHlLSRvgQzRIa80Yi');
insert into order_line values (5, 2, 1, 11, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, '4gT8Po4BuGuXXV2A3tJbe9u5');
insert into order_line values (5, 2, 1, 12, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'lsIpvvYGquOA0QoyVXPhdE2a');
insert into order_line values (6, 2, 1, 1, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'aIpu7EJHFlmrhGQgmYOIvQn5');
insert into order_line values (6, 2, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'v7iljRMYjxKBWVRPbzFSjs4d');
insert into order_line values (6, 2, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LkAbcPM4GJFQMrrUcZlZYPDJ');
insert into order_line values (6, 2, 1, 4, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ZcOHLchO4jIBoJMSieXqUDY0');
insert into order_line values (6, 2, 1, 5, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JagbUAdFXVm9WI9FAkUIscgL');
insert into order_line values (6, 2, 1, 6, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'u5XApCvZznb6fe7u82kmRLcI');
insert into order_line values (6, 2, 1, 7, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'pRudZwNLvgNDRjR5yQVcGELk');
insert into order_line values (6, 2, 1, 8, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, '4AgYXXUV3REauUUNAJ6ynuuf');
insert into order_line values (7, 2, 1, 1, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'j8KJs68GYxdHHPCN5quWH0Mn');
insert into order_line values (7, 2, 1, 2, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'XvjwbISXakuTm4eM8pNMIo4Y');
insert into order_line values (7, 2, 1, 3, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'QnbCJh3692wNGVwfWHnem9pN');
insert into order_line values (7, 2, 1, 4, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'HylrA775gqvonBNAWW2nQOTK');
insert into order_line values (7, 2, 1, 5, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'XWGFkpxczk6WIg4L4qjMOlAD');
insert into order_line values (7, 2, 1, 6, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'c2OqwMOBAhC5tOCcBL0SyqXP');
insert into order_line values (7, 2, 1, 7, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'pEsC59HkPl3roBPYA8QpCI5u');
insert into order_line values (7, 2, 1, 8, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'F7ATPSQwTlZHCTaihkEhRxh2');
insert into order_line values (7, 2, 1, 9, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, '2o4REOpy72bCzVZecPOff7fI');
insert into order_line values (8, 2, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'BeAVVlRvLSQpeqjgZANafk10');
insert into order_line values (8, 2, 1, 2, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'nG649o4xFxb5bDrY51ISmp85');
insert into order_line values (8, 2, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, '5fYZ4UpaKU3PH5mz1ajNCIho');
insert into order_line values (8, 2, 1, 4, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 't85xvoYj2SfzJDuFQPbBbDWn');
insert into order_line values (8, 2, 1, 5, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Q3MW7kNKuoTdmxCaC5QByg40');
insert into order_line values (8, 2, 1, 6, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'fGdJgJUN5OXVzXkgsHn02zVD');
insert into order_line values (8, 2, 1, 7, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'uvyIqVm1ZYdzMpkJQrFgolr1');
insert into order_line values (8, 2, 1, 8, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'DsdsWFohg0SoLQbpp2lVd3aA');
insert into order_line values (8, 2, 1, 9, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'gfRYvDM86yyxfC1pqXAKP8uj');
insert into order_line values (8, 2, 1, 10, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'rSmBn0bHjf3RLGHHZtRnfpkU');
insert into order_line values (8, 2, 1, 11, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'TUtGRppAj393ii4nU5mITxig');
insert into order_line values (8, 2, 1, 12, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'uoZnv5zn1OO9Z5mXwgv7apow');
insert into order_line values (8, 2, 1, 13, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'w9jnjsX3bBo2ArVemnDW0dmB');
insert into order_line values (8, 2, 1, 14, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ML0DOpClzZNr2tcn14YiaHGF');
insert into order_line values (9, 2, 1, 1, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'EVdiNFYKZuacX2MuPnrHCGM2');
insert into order_line values (9, 2, 1, 2, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'e3SShAQ9clkl75Ao34a5Nq86');
insert into order_line values (9, 2, 1, 3, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'SZZprimnEd9mvHfnooyQNZNO');
insert into order_line values (9, 2, 1, 4, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'PdArdb5HvKVvaPnDVtrQPJ6c');
insert into order_line values (9, 2, 1, 5, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Bkz7T9wS2IWcyeUXGNNKbG7T');
insert into order_line values (9, 2, 1, 6, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'BrVznZHRqs5K6ZWsrFitDo0a');
insert into order_line values (9, 2, 1, 7, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ouEL7y4WIRFbBbzUvnDlOGyq');
insert into order_line values (9, 2, 1, 8, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'h0vQ1iSF7PTRpCOPUOw55vlv');
insert into order_line values (9, 2, 1, 9, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '5aL1bjsyxgF397qv7THZ1CCL');
insert into order_line values (9, 2, 1, 10, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'atTBaxihhuD2T1GBXoZQdyWv');
insert into order_line values (10, 2, 1, 1, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'A3asB07JiENdsJ3mhk55cAF2');
insert into order_line values (10, 2, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hzM6HR0viM6wkgnfg37AZefm');
insert into order_line values (10, 2, 1, 3, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hcV8rOxtN3WSbDOoyhlzlEbh');
insert into order_line values (10, 2, 1, 4, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'QJwy5ZwzzsbqnJTKkb9vgue8');
insert into order_line values (10, 2, 1, 5, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, '8BLxTjTNjlDNF76zkpv9gs6i');
insert into order_line values (1, 3, 1, 1, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '68qbcDWvMhr7BcKhadzkG2iK');
insert into order_line values (1, 3, 1, 2, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'tgFakUWI3Ay66NnZgYym6l3s');
insert into order_line values (1, 3, 1, 3, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, '4oHS3QyaMd4iuXaOZyZV0LI9');
insert into order_line values (1, 3, 1, 4, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'CKT72EvBwhPJ4QdYzee33wY2');
insert into order_line values (1, 3, 1, 5, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'fl4Qu8Xu7thsAveIh1TQno5e');
insert into order_line values (1, 3, 1, 6, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'qXGEFMUZsOZnWqk7iKLtsSSY');
insert into order_line values (1, 3, 1, 7, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'PEjJGaWVV5wcvFePssvl5bZr');
insert into order_line values (2, 3, 1, 1, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'QHJfx8maw60mAxeC3oDV6T7n');
insert into order_line values (2, 3, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kQIamE0trln0NVG1u18wmYDI');
insert into order_line values (2, 3, 1, 3, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'EfF2HsWENXbMxi6pfRWG6itY');
insert into order_line values (2, 3, 1, 4, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'vNXMw7D77I16ncn3WRak8toK');
insert into order_line values (2, 3, 1, 5, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'OhKbdv5nnGMu6OVWS6vcoeOE');
insert into order_line values (2, 3, 1, 6, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'lvjZ5qYTLrErGC66s1RF0pvj');
insert into order_line values (2, 3, 1, 7, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LOCWD8vb72sxQ1mctLLaXEcr');
insert into order_line values (2, 3, 1, 8, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'C1kohE28PcRt7RgSXK1xpEDq');
insert into order_line values (2, 3, 1, 9, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'jE7vh5MBiI11CdI9U4xymVwX');
insert into order_line values (2, 3, 1, 10, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'MPV0ryCjy4lAsX1o1mzCSPLS');
insert into order_line values (2, 3, 1, 11, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'yv8AQ6mfBJwHzzvNfmmR2Ugu');
insert into order_line values (3, 3, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'P0T66lx74RkL7RtsrNLosPFh');
insert into order_line values (3, 3, 1, 2, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Ylv5HMqpzRFmuCnpBc0l0P5u');
insert into order_line values (3, 3, 1, 3, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '5jQGeNAcwepHzZUHXg5S89G9');
insert into order_line values (3, 3, 1, 4, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ED0rMSbm1qsa6CirnIGVHtDO');
insert into order_line values (3, 3, 1, 5, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '9fZbWWoAM0pPViJOoP8GyvtJ');
insert into order_line values (3, 3, 1, 6, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JW4SxAbAmMd7htPfwdZ1gWsj');
insert into order_line values (3, 3, 1, 7, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'iaSNtHig9aVWJkzzfdyWWixt');
insert into order_line values (3, 3, 1, 8, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'UdqAmpNdXyxY74crseekR0BE');
insert into order_line values (3, 3, 1, 9, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hdfoBguHwtAPFtRKEVr4Ma6q');
insert into order_line values (3, 3, 1, 10, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Fr3cnvspGlOUe2Sr2ibfdp9y');
insert into order_line values (4, 3, 1, 1, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ZeCXoJllYhulyXQ6Cka0qS4c');
insert into order_line values (4, 3, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'sBGm4qdVKylVn9wsQ47P6Xuv');
insert into order_line values (4, 3, 1, 3, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'FT1gSoue1v8d9VIlyplCCADi');
insert into order_line values (4, 3, 1, 4, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'nMBUB7fu7zPtcR5C96lhtkzX');
insert into order_line values (4, 3, 1, 5, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'yi1wsieqds8FpGH3FtNtg1jw');
insert into order_line values (4, 3, 1, 6, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, '8QyfU2F7oMKdXenzWU0u0PzZ');
insert into order_line values (4, 3, 1, 7, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'QhZSkhUZDgO56WRdpu3Yik54');
insert into order_line values (4, 3, 1, 8, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'uv8pX0bRCjlWc2Ha2jZTKT9o');
insert into order_line values (4, 3, 1, 9, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, '1ZoUYf5OFKscd2qn42A2TFNY');
insert into order_line values (4, 3, 1, 10, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'hcKkbULlbUjYo79RFggltsSd');
insert into order_line values (4, 3, 1, 11, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'sECLltWYdNERNEN8gni7crBH');
insert into order_line values (4, 3, 1, 12, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'lfpmWvTbPs2VVA5oCx1DmRoW');
insert into order_line values (4, 3, 1, 13, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'zlrmRwoOQUTTLMdFZ0UrPu5b');
insert into order_line values (4, 3, 1, 14, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'rc22P62WiRSzggNxgGSx6WiX');
insert into order_line values (5, 3, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, '1kKp0T6jaeM2I55zaCKyEahS');
insert into order_line values (5, 3, 1, 2, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LQe7uUeChecpkQX5OwUeP8ZS');
insert into order_line values (5, 3, 1, 3, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ov4m62VgwsM5EHd7ur38YLFa');
insert into order_line values (5, 3, 1, 4, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'CqQ4iHA2WtVW8nzCvGFGUbO2');
insert into order_line values (5, 3, 1, 5, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mbmBc4TNZ7SycVE5PIq0KMak');
insert into order_line values (6, 3, 1, 1, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mMp3HOP9YoOewvgmR5im4jvE');
insert into order_line values (6, 3, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'LmblBugNhpYidWerADiM3vU5');
insert into order_line values (6, 3, 1, 3, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mXmlScKJLWtJFXgO46BmJu7v');
insert into order_line values (6, 3, 1, 4, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 't9PgoS3XUJ3cuYeQD91kLrc7');
insert into order_line values (6, 3, 1, 5, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'bqIgPfmNwTNKJvl4WWnITZpm');
insert into order_line values (6, 3, 1, 6, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'UfzRPYE8kUOmBtHDHPQLsFs1');
insert into order_line values (6, 3, 1, 7, 6, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Y35WA6I6DUaAOU4hVx4Tvada');
insert into order_line values (6, 3, 1, 8, 8, 1, '2023-06-23 10:26:17', 5, 0.000000, 'pO5TKeKR0M3lkgElzALZMlaM');
insert into order_line values (6, 3, 1, 9, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'EYGipoanO3SmbId2WOk2DDKy');
insert into order_line values (7, 3, 1, 1, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Ptlp3Ot83M4WH3IcthGlX8Th');
insert into order_line values (7, 3, 1, 2, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'gInreZuA17rMNNoazd2B4REi');
insert into order_line values (7, 3, 1, 3, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'G0FjntLT7Rqb4bvGRZMym03V');
insert into order_line values (7, 3, 1, 4, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, '3rftnP6inSuUzhVHZFwh4eu2');
insert into order_line values (7, 3, 1, 5, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'F7wiu8ZHVrRUSJ6pwFc6Keng');
insert into order_line values (7, 3, 1, 6, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'OqtpdBr2mPbq9eWLTAJYHKmx');
insert into order_line values (7, 3, 1, 7, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Q96OIdufDGwPvN5Wde4orDua');
insert into order_line values (7, 3, 1, 8, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'SYD9p4vF2y5uYdh3HU1QIcad');
insert into order_line values (7, 3, 1, 9, 1, 1, '2023-06-23 10:26:17', 5, 0.000000, 'kq1FimaC3wACAUNbZSKqUQEa');
insert into order_line values (7, 3, 1, 10, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'XaV2pq95gIgMNTNINwcPIkrm');
insert into order_line values (7, 3, 1, 11, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, '43eSyoBqOxDy2JvBccrCLqRy');
insert into order_line values (8, 3, 1, 1, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'b0a2kSyHdcragoq8QmHIQxAM');
insert into order_line values (8, 3, 1, 2, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'r8KAfzm2sLWKfxQKPkS4YmJC');
insert into order_line values (8, 3, 1, 3, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, 'mUNBfriIb7IU0Zm7F29z7nbR');
insert into order_line values (8, 3, 1, 4, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'aBSXLVB7mdkpJTcr7jkmuXLg');
insert into order_line values (8, 3, 1, 5, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'WHGNYg7YnhMA0EAvgJFxfAoP');
insert into order_line values (8, 3, 1, 6, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'tipD5KJA3Q2OElefgqMAbv57');
insert into order_line values (8, 3, 1, 7, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'jgJN9BCPeMe4YjmuD0A2OyG2');
insert into order_line values (8, 3, 1, 8, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'RbOUSTRrxtilWZ2qQGhkcdan');
insert into order_line values (8, 3, 1, 9, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'pqorX4HlZ1uTxxqyrLZbE9yB');
insert into order_line values (8, 3, 1, 10, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'gX5ILKZyUYaRVvRmd95bCLnd');
insert into order_line values (8, 3, 1, 11, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'JTGdwlA5qCCdJ6x5QkNBLnCB');
insert into order_line values (8, 3, 1, 12, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ALRXIPsq9qj1TOmoLXKyCTnQ');
insert into order_line values (9, 3, 1, 1, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, '57QvOSZHAQAMfBWqmfYEK3Du');
insert into order_line values (9, 3, 1, 2, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'CBdLAi3Ov4DORK2sLGRgEslU');
insert into order_line values (9, 3, 1, 3, 2, 1, '2023-06-23 10:26:17', 5, 0.000000, 'GoLj0vB8xk9t7Eo6lLTZlGzw');
insert into order_line values (9, 3, 1, 4, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'EmYzy828Pe9sgJsPL3ixkRXS');
insert into order_line values (9, 3, 1, 5, 7, 1, '2023-06-23 10:26:17', 5, 0.000000, 'ksw3LYdIjI0WuqmHPCNKcnBM');
insert into order_line values (9, 3, 1, 6, 9, 1, '2023-06-23 10:26:17', 5, 0.000000, 'miSJRe2oWz9mJGG5sztME7X7');
insert into order_line values (10, 3, 1, 1, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'TXvKV3Bcg92uksQf52pUCyOD');
insert into order_line values (10, 3, 1, 2, 5, 1, '2023-06-23 10:26:17', 5, 0.000000, 'Ll2ZMt4rIIP2D5CySmRcI6Dp');
insert into order_line values (10, 3, 1, 3, 10, 1, '2023-06-23 10:26:17', 5, 0.000000, 'AAjObCmnwEvSE2AKfTec76Rj');
insert into order_line values (10, 3, 1, 4, 3, 1, '2023-06-23 10:26:17', 5, 0.000000, '23RaTp79adthFrstXpze5hhd');
insert into order_line values (10, 3, 1, 5, 4, 1, '2023-06-23 10:26:17', 5, 0.000000, 'pokN2Z8NTvtukyPu6ETFknk8');
insert into item values (1, 2379, 'Dm00IYs4zMMKmS6CjHu9BrcU', 612.625000, 'COzG2gcflKg2SQML3qLOqEE5aZPGfCxLVfLxAxSB0iUCDPAbUz');
insert into item values (2, 2299, 'DBRSDoOhsVA3620tKAmp77xF', 298.250000, 'V0JOWpwrLHKCxKde0jfxE1PWoX1htJ1by7XI8g2lGsphobKfjK');
insert into item values (3, 8600, '8B5aprLZDsKmP3afjUTU0xAJ', 850.312500, 'G8jnH6xzJKBJn6kOSOCDv2bEp9at4Fx1HT6Mii5G4tDEWLnUkB');

-- create static_checkpoint;

insert into item values (4, 962, 'bTbYhnKmU4cy4EegPPo7UFJ6', 540.500000, 'gRHBUtF2LfWBcCpY49m94AOttA7NuN0GU0wWfilUD3Y3Dt1lu7');
insert into item values (5, 1036, 'biAjotQSxBWdGBLWPWRCiAZu', 513.250000, 'bRdpbqq5BQveiecxBMziBAFRz4GYwaD0k6IQboRenenbQ8S1aE');
insert into item values (6, 8662, 'Nrn4VMyrEhZaonaCl8PBatgA', 744.500000, 'AxY8Y46JRRZviTpmzKkBdt27IpPgWunhoHRs1QlVjljQZgClFt');
insert into item values (7, 1935, 'ROySX6yeUaUV7zbIYIpJd9UF', 382.500000, 'UI3RJtqPFpcyWIIqyUkT0pIfs7533W9LbKMrBFVTMlCsubrrIh');
insert into item values (8, 4378, '1APEIKZbbstthvBlV1ANTqRS', 871.125000, 'JaqcUO6Hx5zcCBZZdFfYXKX5HWH4WoxaqD9knhc8IPp8kNqw7D');
insert into item values (9, 469, 'wizoN1hW7HMwoqlnqZvBbuNu', 405.625000, 'IztCkOs6ShW0hUHD9B9nvGHnBD4j0zk7gxXN8lS5nGKo0SdnJD');
insert into item values (10, 7877, '0N325RzjlNjvRhGYFyjooI6R', 425.250000, 'Z5YQDkzFowJIwNnw1zhdCpzPao5CozVVV43OO5DA09gP2PsOWL');
insert into stock values (1, 1, 47, 'FSuPuH1qLC9dZAbM9ll7FrnN', 'tTHaEV9ckPUrdthPrHQ1l7UL', 'P868tchmPAjM0rB75HnFvtYU', 'nAqwM0ZUHUfWlG7LjPZvQxFT', 'ycgLlgZFKHVFPa8ceFbDiyxz', 'H2EFa8ApzocIvevdml9CXZXM', 'uvOEoj4QSTu1zpXbXQ3Eub28', 'AI8NFpPr2FUtshvc2MVVczSm', 'vDNma7E7VA8lQ4jRMu8w48mi', 'DDwS219D2r853haZzEQWA22S', 0.000000, 0, 0, '3Qj4sU0MPfp6VKu0l6h8rsRzpLUpxPsHv09B0lnlSs6aqB9c3l');
insert into stock values (2, 1, 52, 'jq6J2rSW1HiyVWpukLdgG68j', 'rgU4krInEGhpkFJxk1jwomle', '3axEyjXUeIhhHaplfwBT9bES', 'IyfiuiAuOocX0fLwbpbQspiE', 'zAc1TN14lvIb6vDyaEfmBLUo', 'dI2TcyKtAuUSQM2BKMqiryCh', 'CQsJix08nBrT17pMwgOUbolk', 'hwOEkoj9AnDjqwwr3no8sWOD', 'GvbQq4pGCTwPqAGpQpI9DhwG', 'XhLH7b95D3SdRXcjCYm1piBN', 0.000000, 0, 0, 'mOvlGJOTuU7sNjzu1RQUECmPQaWpRfU3ZuMQPayoLitLSGsizx');
insert into stock values (3, 1, 57, 'Nq6LfGfqmymKTg16TmZDGg6Z', 'bEv8dGbt3MFiNaDLmiYw8qqo', 'YSsfG7RQxoSUniWui8RiWcPY', 'BhqbkGgdRy1IzMUVTDxbWM7q', 'pPzTXFErf4vQ1n4NTJ1NVyNV', '2wlxsoVuKBGrsMVc4urBkBzN', 's1AYmpI8BNw5yzJPhAM6o6pi', 'N9c7a7oyTHlikL1S28pdVWYv', 'I7bdLTKVIHm70qt7n4nBlgIX', 'GpNkAgm7PCfqJORrhJaVtgNW', 0.000000, 0, 0, 'U0g95v8S3cWR5sL4465DINjE8YqOD2wvP7d52Mu9l3Q1R3UuMy');
insert into stock values (4, 1, 85, 'bQSIt3QXgj1KCL5mSwN7iiKc', '7VisBW9nKhyMVsusFRzKvWya', 'wTjcQHix9VEP9V5E1itsOW9a', 'VlzNUBZLuQslJr4pRhPIeHkt', 'jEPQBr3OzagJuqiboi6trHhH', 'kk3IzBmEHjciMfO8FLBX6FBN', '9bj0KtnjM1fdi7RdkmuRUQwF', 'jaXPfBSG0qr1I6m82DZDGoHN', 'vUjdEg9yCU8r9QzHNBsIBSQy', '3qOaRXuzXfZOzeUP2l2kcSzc', 0.000000, 0, 0, 'XzH7Iwj7wBLlAY6M5gwtCDxlC6SsPODOFDjJ73yJHAFqgmWCfE');
insert into stock values (5, 1, 55, 'FZse2Zb7vXeXeSa81Ltt3mqx', 'B5M6NNlB5fNNuYWadXxOCsFP', 'qf9wRwm9QGMcizYe9lj7v6aG', '835Q1OwvIv30XE9PfJh3oMa3', 'Lid6fVyv4yjk1adWfYtjwOU7', 'PsFwRPAaSPIKHhI6unn0vopQ', 'CCjRQWvMhC8kG1FUuoUhfvd4', 'mV5gOwoLiF2FC9MCQK7NyuDU', 'HwGVfHiLJ1OhjXPrM2uuNKnC', 'CgwXqk14ruVchpkUqeQTmA6U', 0.000000, 0, 0, 'mGWZXs9f0ZYQFLV9GHRA4wpatNfaRut84DgEvIEe88bSq8MFUe');
insert into stock values (6, 1, 50, 'PyvYXn1CEb3iM6GKs0iXbhpo', 'JwmpjkTKwvRrZtEQlawZaLhf', 'YjFKIrtrSvY3k3RlpmWs1nSO', 'gnhmanFiapb3RZeA7e6s1re3', 'xsvak0clxUH4p6cextjGUQaF', 'WfNyQyK27sdUvIXugdMs79o1', '9DcvkEGzUSb6ljxJo3Q50vyM', '5p7aFqqzZf9LvZytZCCDsF3E', 'KJphJpLbUwQcpalUaAER9uCF', 'vUCg3TX3L2lSH7v8ABXFjWqF', 0.000000, 0, 0, 'dwg5WUk5gguCX8QF469ASkZQPKxr2E1iOsmJNSwagNzaIiBIqJ');
insert into stock values (7, 1, 39, 'xE5mdO7VSV0xpQo3ysava8DT', '1DhwqyWqBNIgJjpzy0zMPEom', '8F0WkmfMuR4L17r7sOZHmd5M', 'te2yfTNNyZuxFAtaXjFT3pxc', 'wXiy5vitmsHnNgbfQBsd7EgV', 'QVQvIvjgJR9KAZwMTQpnukXy', 'P64pXTBeNrDhzmVB7X8GH8AZ', '7KyRx4AgAD6UNPlTBwJkHSnM', 'ZHky2kYd25U4eKeqeANUFiti', 'bzO4ZSZ0c2jpM3n3P1q6Z7uX', 0.000000, 0, 0, 'Xj7vWc5K5HSJ88jF16i8NfFcJ1p0AFGJVNAGimALqY0BJ86fxV');
insert into stock values (8, 1, 93, '5eNlgyrklVW4px4OmAlEi1vM', 'YzkxgTNqZuzyrWYvj8Lo40jv', '7VxSDjSTpz9MaFGAAk9SY6Z7', 'ERBjitTwbQs6h6dvgES3UgKI', 'RiDgDBnkXVXaBt3CatIoqDeC', 'CYXW4PzZFmfiPAOQIpyePxhC', 'R9askt5XOcqJBjQ1rmLbDFMS', 'vVlN7DFpekPvSnbLieuziC8F', 'gPNbvKJtQ3K8AdoMkKBZOLU3', 'TG6D3IU0fhRdC1dgxneIuwiF', 0.000000, 0, 0, 'E7hkETpe5vgAJYCZw3WxvmxRy2doD93GyEDa3d4BKoT8OmxKHS');
insert into stock values (9, 1, 26, 'DR6K9rnyOnPfu0FszMwgj5rp', 'fYs2yiqKNy1iX1NzfOLfwdLa', 'uTWHsOA6OEGdQ5Xt66ICTTxU', 'HBsSQ31wTSIbSTZyQE51uIQl', 'VjUJPXsmrFDsmyFu8XikkJQS', 'aSXMdBFqfsobXvSQlKsHCM8D', 'XKdSebySefvEQnPPBZ5ELSxe', 'jI9vWdDxGO4RgLYKW9kNeQL0', 'c2MLkoHF5tG1JWi555cSdEDC', 'rxmBjTdWDuxSfYnsL4hJsikf', 0.000000, 0, 0, 'FwdancGIlXU5FuwVF9e120xxgumdQ9Eha6M8RLEb0moq7wfu9D');
insert into stock values (10, 1, 18, '9sJkknZ7Q9ZzfSaJcISORPch', 'qcT9wyo0yC5ouIlSxfDfvFmX', 'ec0ggmih6nZHKt3DJtY9aKUC', 'Zsre6nlBBkbVbMjeQIx1okYE', 'tsZCf505rN2Jl5nm5jxpBgrU', '11nF1q67Hrre9ag3YupYwagJ', 'PhA2EXXZvNGQueQeSjIQGyjR', 'YuipaBNdGACToDryKaxNXvxJ', 'Tdlv5Son4YVIW19noxVwqN9F', 'tJCSEa12ws0iux9msbbujJYF', 0.000000, 0, 0, 'pBoODuHt1wkGKaB6RHxIM6Wjg7GYpMUfplptlp3vcDt0HwC9JY');

begin;
-- select c_discount, c_last, c_credit, w_tax from customer, warehouse where w_id=1 and c_w_id=w_id and c_d_id=2 and c_id=8;
-- select d_next_o_id, d_tax from district where d_id=2 and d_w_id=1;
update district set d_next_o_id=12 where d_id=2 and d_w_id=1;
insert into orders values (11, 2, 1, 8, '2023-06-23 10:26:17', 35, 5, 1);
insert into new_orders values (11, 2, 1);
abort;

-- create static_checkpoint;

begin;
-- select i_price, i_name, i_data from item where i_id=10;
-- select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=10 and s_w_id=1;
update stock set s_quantity=9 where s_i_id=10 and s_w_id=1;
insert into order_line values (11, 2, 1, 1, 10, 1, '2023-06-23 10:26:17', 9, 919.125000, 'DjYI3KByCMWfdLuNz2EJHMQn');
-- select i_price, i_name, i_data from item where i_id=2;
-- select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=2 and s_w_id=1;
commit;


begin;
update stock set s_quantity=7 where s_i_id=2 and s_w_id=1;
insert into order_line values (11, 2, 1, 2, 2, 1, '2023-06-23 10:26:17', 7, 184.500000, 'lt0rwyHNi4u6rN1o1cijvgGB');
-- select i_price, i_name, i_data from item where i_id=4;
-- select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=4 and s_w_id=1;
update stock set s_quantity=1 where s_i_id=4 and s_w_id=1;
insert into order_line values (11, 2, 1, 3, 4, 1, '2023-06-23 10:26:17', 1, 286.125000, '5BRRU3erdWI5pz2i91tEuKGT');
commit;

begin;
-- select i_price, i_name, i_data from item where i_id=3;
-- select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=3 and s_w_id=1;
update stock set s_quantity=10 where s_i_id=3 and s_w_id=1;
insert into order_line values (11, 2, 1, 4, 3, 1, '2023-06-23 10:26:17', 10, 478.312500, '0LDmSD6dkosE6yKNZd39AuI1');
-- select i_price, i_name, i_data from item where i_id=2;
abort;




select * from order_line;
select * from stock;
select * from district;
select * from new_orders;
select * from orders;
select * from order_line where ol_w_id = 1 and ol_d_id = 2 and ol_o_id = 11 and ol_number = 4;

begin;
-- select s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 from stock where s_i_id=2 and s_w_id=1;
update stock set s_quantity=1 where s_i_id=2 and s_w_id=1;
insert into order_line values (11, 2, 1, 5, 2, 1, '2023-06-23 10:26:17', 1, 256.125000, 'xubUG1QlylxR0kQjoSKOhq4F');
delete from order_line where ol_o_id = 11 and ol_d_id = 2 and ol_w_id = 1 and ol_number = 4;
crash