-- 单个聚合函数的GROUP BY测试
create table students (id int, name char(10), age int, score float);
insert into students values(1, '<PERSON>', 20, 95.5);
insert into students values(2, '<PERSON>', 21, 87.0);
insert into students values(3, '<PERSON>', 20, 92.5);
insert into students values(4, '<PERSON>', 21, 88.5);
insert into students values(5, 'Eve', 20, 96.0);

-- 测试GROUP BY分组统计（单个聚合函数）
select age, COUNT(*) as count from students group by age;
select age, MAX(score) as max_score from students group by age;
select age, MIN(score) as min_score from students group by age;
select age, AVG(score) as avg_score from students group by age;

-- 测试HAVING条件过滤
select age, COUNT(*) as count from students group by age having COUNT(*) >= 2;
select age, AVG(score) as avg_score from students group by age having AVG(score) > 90.0;

drop table students;
