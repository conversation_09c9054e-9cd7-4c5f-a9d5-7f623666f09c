/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "buffer_pool_manager.h"

/**
 * @description: 从free_list或replacer中得到可淘汰帧页的 *frame_id
 * @return {bool} true: 可替换帧查找成功 , false: 可替换帧查找失败
 * @param {frame_id_t*} frame_id 帧页id指针,返回成功找到的可替换帧id
 */
bool BufferPoolManager::find_victim_page(frame_id_t* frame_id) {
    // Todo:
    // 1 使用BufferPoolManager::free_list_判断缓冲池是否已满需要淘汰页面
    // 1.1 未满获得frame
    // 1.2 已满使用lru_replacer中的方法选择淘汰页面
    
    assert(frame_id != nullptr);
    //std::unique_lock<std::mutex> lock(latch_);
    /*1.先看free_list是否为空*/
    if(!free_list_.empty())
    {
       *frame_id = free_list_.front();
       free_list_.pop_front();
       return true;
    }

    /*2.淘汰页*/
    if(replacer_->victim(frame_id))
    {
        return true;
    }
    return false;
}

/**
 * @description: 更新页面数据, 如果为脏页则需写入磁盘，再更新为新页面，更新page元数据(data, is_dirty, page_id)和page table
 * @param {Page*} page 写回页指针
 * @param {PageId} new_page_id 新的page_id
 * @param {frame_id_t} new_frame_id 新的帧frame_id
 */
void BufferPoolManager::update_page(Page *page, PageId new_page_id, frame_id_t new_frame_id) {
    // Todo:
    // 1 如果是脏页，写回磁盘，并且把dirty置为false
    // 2 更新page table
    // 3 重置page的data，更新page id
    
    //TODO:若是page不在内存怎么处理
    
    /*1.参数检查*/
    // std::unique_lock<std::mutex> lock(latch_);
    if(page == nullptr)
    {
        return;
    }
    if(page_table_.find(page->get_page_id()) == page_table_.end())
    {
        page_table_[new_page_id] = new_frame_id;
        page->id_ = new_page_id;
        page->reset_memory();
        return;
    }

    /*2.脏页刷盘*/
    if(page->is_dirty())
    {
        disk_manager_->write_page(page->get_page_id().fd,page->get_page_id().page_no,page->get_data(),PAGE_SIZE);
        page->is_dirty_ = false;
    }

    /*3.更新page数据*/
    frame_id_t old_frame_id = page_table_.at(page->get_page_id());
    page_table_.erase(page->get_page_id());
    // free_list_.push_back(old_frame_id);
    page_table_[new_page_id] = new_frame_id;
    page->id_ = new_page_id;
    page->reset_memory();
    page->is_dirty_ = false;
}

/**
 * @description: 从buffer pool获取需要的页。
 *              如果页表中存在page_id（说明该page在缓冲池中），并且pin_count++。
 *              如果页表不存在page_id（说明该page在磁盘中），则找缓冲池victim page，将其替换为磁盘中读取的page，pin_count置1。
 * @return {Page*} 若获得了需要的页则将其返回，否则返回nullptr
 * @param {PageId} page_id 需要获取的页的PageId
 */
Page* BufferPoolManager::fetch_page(PageId page_id) {
    //Todo:
    // 1.     从page_table_中搜寻目标页
    // 1.1    若目标页有被page_table_记录，则将其所在frame固定(pin)，并返回目标页。
    // 1.2    否则，尝试调用find_victim_page获得一个可用的frame，若失败则返回nullptr
    // 2.     若获得的可用frame存储的为dirty page，则须调用updata_page将page写回到磁盘
    // 3.     调用disk_manager_的read_page读取目标页到frame
    // 4.     固定目标页，更新pin_count_
    // 5.     返回目标页

    std::scoped_lock lock{latch_};
    /*1.判断是否在bufferpool*/
    Page *target_page = nullptr;
    if(page_table_.find(page_id) != page_table_.end())
    {
        replacer_->pin(page_table_[page_id]);
        target_page = &pages_[page_table_[page_id]];
        pages_[page_table_[page_id]].pin_count_++;
        return target_page;
    }

    /*2.不在缓冲池*/
    frame_id_t remove_frame = -1;
    if(!find_victim_page(&remove_frame))
    {
        return nullptr;
    }
    // replacer_->pin(remove_frame);
    update_page(&pages_[remove_frame],page_id,remove_frame);
    
    /*3.读数据并写入page*/
    char page_raw_data[PAGE_SIZE];
    disk_manager_->read_page(page_id.fd,page_id.page_no,page_raw_data,PAGE_SIZE);
    memcpy(pages_[remove_frame].data_, page_raw_data, PAGE_SIZE);
    pages_[remove_frame].pin_count_ = 1;
    return &pages_[remove_frame];
}

/**
 * @description: 取消固定pin_count>0的在缓冲池中的page
 * @return {bool} 如果目标页的pin_count<=0则返回false，否则返回true
 * @param {PageId} page_id 目标page的page_id
 * @param {bool} is_dirty 若目标page应该被标记为dirty则为true，否则为false
 */
bool BufferPoolManager::unpin_page(PageId page_id, bool is_dirty) {
    // Todo:
    // 0. lock latch
    // 1. 尝试在page_table_中搜寻page_id对应的页P
    // 1.1 P在页表中不存在 return false
    // 1.2 P在页表中存在，获取其pin_count_
    // 2.1 若pin_count_已经等于0，则返回false
    // 2.2 若pin_count_大于0，则pin_count_自减一
    // 2.2.1 若自减后等于0，则调用replacer_的Unpin
    // 3 根据参数is_dirty，更改P的is_dirty_

    std::scoped_lock lock{latch_};
    if(page_table_.find(page_id) == page_table_.end())
    {
        printf("不在页表\n");
        return false;
    }
    auto pin_count = pages_[page_table_.at(page_id)].pin_count_;
    if(pin_count <= 0)
    {
        // printf("pin count <=  0\n");
        return false;
    }

    // 3. 原子性减少pin计数
    // 使用先减再判断的方式确保操作原子性
    if (--pages_[page_table_.at(page_id)].pin_count_ == 0) {
        replacer_->unpin(page_table_.at(page_id)); // 当pin计数为0时，将页面加入替换器
    }

    if(is_dirty)
    {
        pages_[page_table_.at(page_id)].is_dirty_ = is_dirty;
        return true;
    }
    return true;
}

/**
 * @description: 将目标页写回磁盘，不考虑当前页面是否正在被使用
 * @return {bool} 成功则返回true，否则返回false(只有page_table_中没有目标页时)
 * @param {PageId} page_id 目标页的page_id，不能为INVALID_PAGE_ID
 */
bool BufferPoolManager::flush_page(PageId page_id) {
    // Todo:
    // 0. lock latch
    // 1. 查找页表,尝试获取目标页P
    // 1.1 目标页P没有被page_table_记录 ，返回false
    // 2. 无论P是否为脏都将其写回磁盘。
    // 3. 更新P的is_dirty_

    std::scoped_lock lock{latch_};
    if(page_id.page_no == INVALID_PAGE_ID)
    {
        throw InternalError("BufferPoolManager::flush_page: invalid page_id \n");
    }
    if(page_table_.find(page_id) == page_table_.end())
    {
        return false;
    }
    frame_id_t target_frame = page_table_[page_id];
    disk_manager_->write_page(page_id.fd,page_id.page_no,pages_[target_frame].get_data(),PAGE_SIZE);
    pages_[target_frame].is_dirty_ = false;
    return true;
}

/**
 * @description: 创建一个新的page，即从磁盘中移动一个新建的空page到缓冲池某个位置。
 * @return {Page*} 返回新创建的page，若创建失败则返回nullptr
 * @param {PageId*} page_id 当成功创建一个新的page时存储其page_id
 */
Page* BufferPoolManager::new_page(PageId* page_id) {
    // 1.   获得一个可用的frame，若无法获得则返回nullptr
    // 2.   在fd对应的文件分配一个新的page_id
    // 3.   将frame的数据写回磁盘
    // 4.   固定frame，更新pin_count_
    // 5.   返回获得的page
   std::scoped_lock lock{latch_};
   frame_id_t remove_frame = INVALID_FRAME_ID;
   if(!find_victim_page(&remove_frame))
   {
      return nullptr;
   }

   page_id_t new_page_id = disk_manager_->allocate_page(page_id->fd);
   page_id->page_no = new_page_id;
   
   /*若该frame的数据为脏则写磁盘*/
   if(pages_[remove_frame].is_dirty())
   {
     disk_manager_->write_page(pages_[remove_frame].get_page_id().fd,pages_[remove_frame].get_page_id().page_no,pages_[remove_frame].get_data(),PAGE_SIZE);
   }
   replacer_->pin(remove_frame);
   
   /*构造页*/
   update_page(&pages_[remove_frame],*page_id,remove_frame);
   pages_[remove_frame].pin_count_ = 1;
   return &pages_[remove_frame];
}

/**
 * @description: 从buffer_pool删除目标页
 * @return {bool} 如果目标页不存在于buffer_pool或者成功被删除则返回true，若其存在于buffer_pool但无法删除则返回false
 * @param {PageId} page_id 目标页
 */
bool BufferPoolManager::delete_page(PageId page_id) {
    // 1.   在page_table_中查找目标页，若不存在返回true
    // 2.   若目标页的pin_count不为0，则返回false
    // 3.   将目标页数据写回磁盘，从页表中删除目标页，重置其元数据，将其加入free_list_，返回true
    std::scoped_lock lock{latch_};
    if(page_table_.find(page_id) == page_table_.end())
    {
        return true;
    }
    if(pages_[page_table_[page_id]].pin_count_ != 0)
    {
        return false;
    }

    free_list_.push_back(page_table_[page_id]);
    replacer_->pin(page_table_[page_id]);
    pages_[page_table_[page_id]].reset_memory();
    pages_[page_table_[page_id]].is_dirty_ = false;
    pages_[page_table_[page_id]].id_ = {0,INVALID_PAGE_ID};
    pages_[page_table_[page_id]].pin_count_ = 0;
    page_table_.erase(page_id);
    return true;
}

/**
 * @description: 将buffer_pool中的所有页写回到磁盘
 * @param {int} fd 文件句柄
 */
void BufferPoolManager::flush_all_pages(int fd) {
    std::scoped_lock lock{latch_};
    for(size_t page = 0;page < pool_size_;page++)
    {
        Page& curr_page = pages_[page];
        if(curr_page.get_page_id().page_no != INVALID_PAGE_ID && curr_page.is_dirty())
        {
            disk_manager_->write_page(curr_page.get_page_id().fd,curr_page.get_page_id().page_no,curr_page.get_data(),PAGE_SIZE);
            curr_page.is_dirty_ = false;
        }
    }
}