#line 2 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#line 4 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yyget_lval
#define yyget_lval_ALREADY_DEFINED
#else
#define yyget_lval yyget_lval
#endif

#ifdef yyset_lval
#define yyset_lval_ALREADY_DEFINED
#else
#define yyset_lval yyset_lval
#endif

#ifdef yyget_lloc
#define yyget_lloc_ALREADY_DEFINED
#else
#define yyget_lloc yyget_lloc
#endif

#ifdef yyset_lloc
#define yyset_lloc_ALREADY_DEFINED
#else
#define yyset_lloc yyset_lloc
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap() (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 67
#define YY_END_OF_BUFFER 68
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[237] =
    {   0,
        0,    0,    0,    0,   68,   66,    6,    7,    7,   66,
       66,   60,   60,   60,   60,   60,   62,   60,   60,   61,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   66,
        3,    4,    6,    7,   59,    0,   64,   62,    5,    0,
       65,   62,    1,   65,   65,   63,   57,   58,   56,   61,
       61,   61,   47,   61,   61,   36,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   52,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   61,    2,   63,    5,    5,

        5,   63,   61,   31,   37,   42,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   61,
       61,   61,   61,   61,   61,   27,   61,   61,   61,   41,
       40,   61,   61,   61,   61,   25,   61,   43,   61,   61,
       61,   61,   61,   63,    5,    5,   61,   61,   28,   61,
       61,   61,   61,   17,   16,   61,   33,   61,   61,   61,
       22,   61,   61,   34,   61,   61,   19,   32,   61,   44,
       61,   61,   61,   53,    8,   61,   54,   61,   61,   61,
       11,    9,   61,   39,   61,   61,   61,   61,   55,   29,
       45,   61,   30,   61,   48,   35,   61,   61,   15,   61,

       61,   23,   10,   14,   21,   61,   61,   46,   18,   61,
       26,   13,   24,   20,   61,   38,   61,   61,   61,   12,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   61,
       61,   61,   49,   61,   50,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    4,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    5,    1,    1,    1,    1,    1,    6,    7,
        8,    9,   10,   11,   12,   13,   14,   15,   15,   15,
       15,   15,   15,   15,   15,   15,   15,    1,   16,   17,
       18,   19,    1,    1,   20,   21,   22,   23,   24,   25,
       26,   27,   28,   29,   30,   31,   32,   33,   34,   35,
       36,   37,   38,   39,   40,   41,   42,   43,   44,   36,
        1,    1,    1,    1,   45,    1,   46,   47,   48,   49,

       50,   51,   52,   53,   54,   55,   56,   57,   58,   59,
       60,   61,   36,   62,   63,   64,   65,   66,   67,   68,
       69,   36,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[70] =
    {   0,
        1,    1,    2,    1,    1,    1,    1,    1,    3,    1,
        1,    4,    4,    4,    4,    1,    1,    1,    1,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4
    } ;

static const flex_int16_t yy_base[245] =
    {   0,
        0,    0,  178,  167,  173, 1339,  169, 1339,  166,  149,
      155, 1339,  142,   58,  138,   62,   64,   56,  132,   68,
       72,   80,   86,  106,  131,   91,  146,  142,  112,   99,
      166,  169,  172,  190,  198,  215,  204,  194,  229,  133,
     1339,  124,  127, 1339, 1339,  115, 1339,   75,  282,   94,
       88,  231, 1339,  121,  123,  149, 1339, 1339, 1339,  241,
      245,  255,  258,  295,  298,  261,  302,  305,  314,  318,
      327,  322,  339,  345,  365,  370,  376,  382,  393,  403,
      399,  406,  419,  431,  442,  359,  445,  448,  457,  464,
      468,  471,  490,  494,  497,  520, 1339,   80,    0,  570,

      589,  176,  523,  526,  533,  536,  555,  598,  601,  604,
      607,  612,  544,  616,  626,  629,  643,  632,  652,  667,
      636,  674,  670,  694,  697,  678,  701,  723,  710,  539,
      549,  728,  736,  741,  744,  749,  757,  761,  769,  772,
      775,  789,  793,   76,  855,    0,  803,  799,  796,  821,
      806,  838,  866,  824,  827,  869,  834,  872,  875,  881,
      884,  894,  898,  901,  909,  921,  924,  927,  930,  934,
      937,  949,  953,  959,  966,  973,  976,  979,  990,  995,
      998, 1003, 1008, 1012, 1015, 1018, 1024, 1021, 1038, 1041,
     1044, 1047, 1050, 1057, 1064, 1067, 1070, 1073, 1079, 1089,

     1092, 1095, 1106, 1110, 1113, 1119, 1132, 1122, 1135, 1138,
     1144, 1149, 1154, 1157, 1160, 1163, 1166, 1170, 1175, 1187,
     1190, 1193, 1198, 1201, 1212, 1219, 1204, 1232, 1236, 1246,
     1254, 1259, 1262, 1266, 1274, 1339, 1316, 1320,   83, 1322,
       79, 1326, 1330, 1334
    } ;

static const flex_int16_t yy_def[245] =
    {   0,
      236,    1,  237,  237,  236,  236,  236,  236,  236,  236,
      238,  236,  236,  239,  239,  240,  239,  236,  236,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  239,
      236,  236,  236,  236,  236,  238,  236,  236,  242,  239,
      240,  239,  236,  240,  240,  239,  236,  236,  236,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  236,  236,  243,  242,

      244,  239,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  236,  244,  145,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,

      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,  241,  241,  241,  241,  241,
      241,  241,  241,  241,  241,    0,  236,  236,  236,  236,
      236,  236,  236,  236
    } ;

static const flex_int16_t yy_nxt[1409] =
    {   0,
        6,    7,    8,    9,   10,   11,   12,   12,   12,   13,
       12,   14,   15,   16,   17,   12,   18,   12,   19,   20,
       21,   22,   23,   24,   25,   26,   27,   28,   29,   30,
       31,   32,   30,   33,   30,   30,   34,   35,   36,   37,
       38,   39,   30,   30,   40,   20,   21,   22,   23,   24,
       25,   26,   27,   28,   29,   30,   31,   32,   30,   33,
       30,   34,   35,   36,   37,   38,   39,   30,   30,   49,
       53,   51,   52,   57,   58,   55,   56,   51,   52,   50,
       50,   51,   60,   50,   50,   51,   50,   98,   61,   48,
      144,   50,   50,   51,  144,   65,  236,   50,   50,   51,

       62,   55,   50,   50,   51,   63,   67,   51,   64,   70,
       50,   50,   51,   68,   61,   66,   69,   50,   50,   51,
       47,   65,   71,   50,   50,   51,   62,   77,   43,  236,
       63,  236,   67,   64,   55,   70,   55,   97,   72,   68,
       66,   69,   50,   50,   51,   81,   51,   71,   73,   59,
       74,   51,   77,   50,   50,   51,   48,   50,   50,   51,
       47,   75,   51,  102,   72,   78,   45,   76,   44,   79,
       43,   81,  236,   73,   80,   42,   74,   50,   50,   51,
       50,   50,   51,   50,   50,   51,   42,   75,   84,   51,
      102,   78,   76,   82,  236,   79,   85,  236,  236,   83,

       80,   50,   50,   51,   86,   50,   50,   51,   87,   50,
       50,   51,  236,   95,   84,   50,   50,   51,  236,   82,
      236,   89,   85,   88,   90,   83,   50,   50,   51,  236,
       86,  236,  236,   87,   92,  236,  236,   91,   94,   95,
       50,   50,   51,   56,   51,   52,  236,   89,  236,   88,
       90,   93,   50,   50,   51,   96,   50,   50,   51,  236,
       92,  236,   91,  236,   94,  236,   50,   50,   51,   50,
       50,   51,   50,   50,   51,  236,   93,  104,  103,  105,
      236,   96,   99,   99,  236,   99,   99,   99,   99,   99,
       99,   99,   99,  236,  236,  101,  236,   99,   99,   99,

       99,  236,  236,  104,  103,  105,   50,   50,   51,   50,
       50,   51,  236,   50,   50,   51,   50,   50,   51,  236,
      106,  108,  236,  107,  236,   50,   50,   51,  236,   50,
       50,   51,  236,   50,   50,   51,  109,  111,   50,   50,
       51,  115,  236,  236,  110,  236,  106,  108,  112,  107,
       50,   50,   51,  236,  236,  113,   50,   50,   51,  236,
      114,  236,  109,  111,  236,  236,  116,  115,  236,  110,
       50,   50,   51,  117,  112,  118,   50,   50,   51,  236,
      113,   50,   50,   51,  236,  236,  114,   50,   50,   51,
      236,  236,  116,   50,   50,   51,  236,  236,  119,  117,

      236,  118,  236,  120,   50,   50,   51,  236,  236,  121,
       50,   50,   51,  236,   50,   50,   51,   50,   50,   51,
      236,  236,  122,  123,  119,  124,  127,  236,  236,  120,
       50,   50,   51,  236,  236,  121,  236,  128,  129,  236,
      125,  126,   50,   50,   51,  236,  236,  122,  236,  123,
      236,  124,  127,   50,   50,   51,   50,   50,   51,   50,
       50,   51,  236,  128,  129,  125,  126,  132,   50,   50,
       51,  236,  236,  130,  131,   50,   50,   51,  133,   50,
       50,   51,   50,   50,   51,  236,  236,  134,  135,  236,
      236,  139,  236,  132,  236,  136,  236,  137,  130,  138,

      131,   50,   50,   51,  133,   50,   50,   51,   50,   50,
       51,  236,  236,  134,  135,  236,  141,  139,  236,  236,
      136,  236,  236,  137,  236,  138,  236,  142,  236,  140,
      236,   50,   50,   51,   50,   50,   51,   50,   50,   51,
      236,  236,  141,  143,   50,   50,   51,   50,   50,   51,
       50,   50,   51,  142,  140,   50,   50,   51,  236,  147,
       50,   50,   51,  236,  236,  154,   50,   50,   51,  143,
       99,   99,  236,   99,   99,   99,   99,   99,   99,   99,
       99,  236,  148,  101,  147,   99,   99,   99,   99,   99,
       99,  154,   99,   99,   99,   99,   99,   99,   99,   99,

      236,  236,  146,  236,   99,   99,   99,   99,  148,   50,
       50,   51,   50,   50,   51,   50,   50,   51,   50,   50,
       51,  236,  236,   50,   50,   51,  152,   50,   50,   51,
      236,  236,  150,  236,  149,  153,  151,   50,   50,   51,
       50,   50,   51,   50,   50,   51,  156,   50,   50,   51,
      155,  236,  152,  236,   50,   50,   51,  236,  150,  149,
      236,  153,  151,   50,   50,   51,  236,  157,  236,  159,
      236,  160,  156,  158,  236,  162,  155,  236,   50,   50,
       51,   50,   50,   51,  236,   50,   50,   51,  236,   50,
       50,   51,  157,  236,  159,  236,  236,  160,  161,  158,

      162,  163,  236,  236,  164,   50,   50,   51,   50,   50,
       51,  167,   50,   50,   51,  236,  236,  165,  236,  236,
      166,   50,   50,   51,  161,  236,  236,  163,  236,  236,
      164,  236,  170,  168,   50,   50,   51,  167,  236,   50,
       50,   51,  236,  165,  236,  236,  166,   50,   50,   51,
      169,  171,   50,   50,   51,   50,   50,   51,  170,  168,
       50,   50,   51,  236,  173,  236,  172,  236,   50,   50,
       51,  174,   50,   50,   51,  236,  169,  171,  236,  236,
       50,   50,   51,   50,   50,   51,   50,   50,   51,  236,
      173,  236,  172,  236,  178,  177,  236,  174,  175,  176,

       50,   50,   51,  236,   50,   50,   51,   50,   50,   51,
       50,   50,   51,  236,   50,   50,   51,   50,   50,   51,
      178,  177,  236,  175,  236,  176,  236,  236,  179,  180,
      236,  182,   50,   50,   51,   50,   50,   51,   50,   50,
       51,  181,  236,  236,  184,   50,   50,   51,  183,   50,
       50,   51,  236,  179,  180,   99,   99,  182,   99,   99,
       99,   99,   99,   99,   99,   99,  181,  236,  146,  184,
       99,   99,   99,   99,  183,  236,  185,   50,   50,   51,
       50,   50,   51,   50,   50,   51,   50,   50,   51,  236,
      236,  188,   50,   50,   51,   50,   50,   51,  189,  187,

      236,  185,  236,  236,  186,   50,   50,   51,  236,   50,
       50,   51,   50,   50,   51,  236,  236,  188,  236,  190,
       50,   50,   51,  236,  189,  187,  236,  236,  191,  186,
      192,  236,   50,   50,   51,   50,   50,   51,   50,   50,
       51,   50,   50,   51,  190,   50,   50,   51,   50,   50,
       51,  193,  236,  236,  191,  236,  192,  194,  236,  236,
       50,   50,   51,  236,   50,   50,   51,  236,  195,  197,
       50,   50,   51,  196,  198,  236,  193,   50,   50,   51,
      236,  236,  194,  236,   50,   50,   51,   50,   50,   51,
       50,   50,   51,  195,  236,  197,  199,  236,  196,  236,

      198,   50,   50,   51,  236,  236,   50,   50,   51,   50,
       50,   51,  236,  201,   50,   50,   51,  200,  202,   50,
       50,   51,  199,   50,   50,   51,   50,   50,   51,   50,
       50,   51,   50,   50,   51,   50,   50,   51,  204,  201,
      236,  205,  200,  236,  202,  236,  203,  206,  207,   50,
       50,   51,   50,   50,   51,   50,   50,   51,   50,   50,
       51,   50,   50,   51,  204,  236,  236,  205,   50,   50,
       51,  203,  208,  206,  207,   50,   50,   51,   50,   50,
       51,   50,   50,   51,   50,   50,   51,  236,  236,  210,
       50,   50,   51,  236,  236,  209,  236,  236,  208,  236,

       50,   50,   51,   50,   50,   51,   50,   50,   51,  236,
      236,  211,  213,  236,  236,  210,  212,   50,   50,   51,
      209,   50,   50,   51,   50,   50,   51,  236,  236,  214,
       50,   50,   51,   50,   50,   51,  211,  236,  213,  236,
      236,  212,  236,   50,   50,   51,   50,   50,   51,   50,
       50,   51,  236,  236,  214,   50,   50,   51,  236,  217,
       50,   50,   51,  215,  216,   50,   50,   51,   50,   50,
       51,   50,   50,   51,   50,   50,   51,   50,   50,   51,
      236,   50,   50,   51,  236,  217,   50,   50,   51,  236,
      216,  236,  218,  221,  236,  220,  236,  219,   50,   50,

       51,   50,   50,   51,   50,   50,   51,  236,  222,   50,
       50,   51,   50,   50,   51,   50,   50,   51,  218,  221,
      236,  220,  219,   50,   50,   51,  236,  223,  236,  224,
       50,   50,   51,  236,  222,  236,  225,  229,  236,  226,
      236,  236,  227,   50,   50,   51,  236,   50,   50,   51,
      228,  236,  223,  236,  224,  230,  236,   50,   50,   51,
      236,  225,  236,  229,  226,   50,   50,   51,  227,  231,
       50,   50,   51,   50,   50,   51,  228,   50,   50,   51,
      236,  230,  232,  236,  234,   50,   50,   51,  233,  235,
      236,  236,  236,  236,  236,  231,  236,  236,  236,  236,

      236,  236,  236,  236,  236,  236,  236,  232,  236,  236,
      234,  236,  236,  236,  233,  235,   41,   41,   41,   41,
       46,   46,   46,   46,   54,   54,  100,  236,  100,  100,
       99,  236,   99,   99,  145,  236,  145,  145,    5,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,

      236,  236,  236,  236,  236,  236,  236,  236
    } ;

static const flex_int16_t yy_chk[1409] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,   14,
       16,   14,   14,   18,   18,   16,   17,   17,   17,   20,
       20,   20,  241,   21,   21,   21,  239,   48,   20,   48,
      144,   22,   22,   22,   98,   21,   51,   23,   23,   23,

       20,   51,   26,   26,   26,   20,   22,   50,   20,   23,
       30,   30,   30,   22,   20,   21,   22,   24,   24,   24,
       46,   21,   23,   29,   29,   29,   20,   26,   43,   54,
       20,   55,   22,   20,   54,   23,   55,   42,   24,   22,
       21,   22,   25,   25,   25,   29,   40,   23,   24,   19,
       25,   15,   26,   28,   28,   28,   13,   27,   27,   27,
       11,   25,   56,   56,   24,   27,   10,   25,    9,   27,
        7,   29,    5,   24,   28,    4,   25,   31,   31,   31,
       32,   32,   32,   33,   33,   33,    3,   25,   32,  102,
      102,   27,   25,   31,    0,   27,   32,    0,    0,   31,

       28,   34,   34,   34,   33,   38,   38,   38,   33,   35,
       35,   35,    0,   38,   32,   37,   37,   37,    0,   31,
        0,   35,   32,   34,   35,   31,   36,   36,   36,    0,
       33,    0,    0,   33,   36,    0,    0,   35,   37,   38,
       39,   39,   39,   52,   52,   52,    0,   35,    0,   34,
       35,   36,   60,   60,   60,   39,   61,   61,   61,    0,
       36,    0,   35,    0,   37,    0,   62,   62,   62,   63,
       63,   63,   66,   66,   66,    0,   36,   62,   61,   63,
        0,   39,   49,   49,    0,   49,   49,   49,   49,   49,
       49,   49,   49,    0,    0,   49,    0,   49,   49,   49,

       49,    0,    0,   62,   61,   63,   64,   64,   64,   65,
       65,   65,    0,   67,   67,   67,   68,   68,   68,    0,
       64,   67,    0,   65,    0,   69,   69,   69,    0,   70,
       70,   70,    0,   72,   72,   72,   68,   69,   71,   71,
       71,   72,    0,    0,   68,    0,   64,   67,   70,   65,
       73,   73,   73,    0,    0,   70,   74,   74,   74,    0,
       71,    0,   68,   69,    0,    0,   73,   72,    0,   68,
       86,   86,   86,   73,   70,   74,   75,   75,   75,    0,
       70,   76,   76,   76,    0,    0,   71,   77,   77,   77,
        0,    0,   73,   78,   78,   78,    0,    0,   75,   73,

        0,   74,    0,   76,   79,   79,   79,    0,    0,   77,
       81,   81,   81,    0,   80,   80,   80,   82,   82,   82,
        0,    0,   78,   79,   75,   80,   81,    0,    0,   76,
       83,   83,   83,    0,    0,   77,    0,   82,   83,    0,
       80,   80,   84,   84,   84,    0,    0,   78,    0,   79,
        0,   80,   81,   85,   85,   85,   87,   87,   87,   88,
       88,   88,    0,   82,   83,   80,   80,   87,   89,   89,
       89,    0,    0,   84,   85,   90,   90,   90,   88,   91,
       91,   91,   92,   92,   92,    0,    0,   89,   89,    0,
        0,   92,    0,   87,    0,   89,    0,   90,   84,   91,

       85,   93,   93,   93,   88,   94,   94,   94,   95,   95,
       95,    0,    0,   89,   89,    0,   94,   92,    0,    0,
       89,    0,    0,   90,    0,   91,    0,   95,    0,   93,
        0,   96,   96,   96,  103,  103,  103,  104,  104,  104,
        0,    0,   94,   96,  105,  105,  105,  106,  106,  106,
      130,  130,  130,   95,   93,  113,  113,  113,    0,  103,
      131,  131,  131,    0,    0,  113,  107,  107,  107,   96,
      100,  100,    0,  100,  100,  100,  100,  100,  100,  100,
      100,    0,  107,  100,  103,  100,  100,  100,  100,  101,
      101,  113,  101,  101,  101,  101,  101,  101,  101,  101,

        0,    0,  101,    0,  101,  101,  101,  101,  107,  108,
      108,  108,  109,  109,  109,  110,  110,  110,  111,  111,
      111,    0,    0,  112,  112,  112,  111,  114,  114,  114,
        0,    0,  109,    0,  108,  112,  110,  115,  115,  115,
      116,  116,  116,  118,  118,  118,  115,  121,  121,  121,
      114,    0,  111,    0,  117,  117,  117,    0,  109,  108,
        0,  112,  110,  119,  119,  119,    0,  116,    0,  118,
        0,  119,  115,  117,    0,  121,  114,    0,  120,  120,
      120,  123,  123,  123,    0,  122,  122,  122,    0,  126,
      126,  126,  116,    0,  118,    0,    0,  119,  120,  117,

      121,  122,    0,    0,  123,  124,  124,  124,  125,  125,
      125,  126,  127,  127,  127,    0,    0,  124,    0,    0,
      125,  129,  129,  129,  120,    0,    0,  122,    0,    0,
      123,    0,  129,  127,  128,  128,  128,  126,    0,  132,
      132,  132,    0,  124,    0,    0,  125,  133,  133,  133,
      128,  132,  134,  134,  134,  135,  135,  135,  129,  127,
      136,  136,  136,    0,  134,    0,  133,    0,  137,  137,
      137,  135,  138,  138,  138,    0,  128,  132,    0,    0,
      139,  139,  139,  140,  140,  140,  141,  141,  141,    0,
      134,    0,  133,    0,  141,  140,    0,  135,  137,  139,

      142,  142,  142,    0,  143,  143,  143,  149,  149,  149,
      148,  148,  148,    0,  147,  147,  147,  151,  151,  151,
      141,  140,    0,  137,    0,  139,    0,    0,  142,  143,
        0,  148,  150,  150,  150,  154,  154,  154,  155,  155,
      155,  147,    0,    0,  151,  157,  157,  157,  150,  152,
      152,  152,    0,  142,  143,  145,  145,  148,  145,  145,
      145,  145,  145,  145,  145,  145,  147,    0,  145,  151,
      145,  145,  145,  145,  150,    0,  152,  153,  153,  153,
      156,  156,  156,  158,  158,  158,  159,  159,  159,    0,
        0,  158,  160,  160,  160,  161,  161,  161,  159,  156,

        0,  152,    0,    0,  153,  162,  162,  162,    0,  163,
      163,  163,  164,  164,  164,    0,    0,  158,    0,  160,
      165,  165,  165,    0,  159,  156,    0,    0,  162,  153,
      163,    0,  166,  166,  166,  167,  167,  167,  168,  168,
      168,  169,  169,  169,  160,  170,  170,  170,  171,  171,
      171,  165,    0,    0,  162,    0,  163,  166,    0,    0,
      172,  172,  172,    0,  173,  173,  173,    0,  169,  172,
      174,  174,  174,  171,  173,    0,  165,  175,  175,  175,
        0,    0,  166,    0,  176,  176,  176,  177,  177,  177,
      178,  178,  178,  169,    0,  172,  176,    0,  171,    0,

      173,  179,  179,  179,    0,    0,  180,  180,  180,  181,
      181,  181,    0,  179,  182,  182,  182,  178,  180,  183,
      183,  183,  176,  184,  184,  184,  185,  185,  185,  186,
      186,  186,  188,  188,  188,  187,  187,  187,  185,  179,
        0,  186,  178,    0,  180,    0,  183,  187,  188,  189,
      189,  189,  190,  190,  190,  191,  191,  191,  192,  192,
      192,  193,  193,  193,  185,    0,    0,  186,  194,  194,
      194,  183,  192,  187,  188,  195,  195,  195,  196,  196,
      196,  197,  197,  197,  198,  198,  198,    0,    0,  197,
      199,  199,  199,    0,    0,  194,    0,    0,  192,    0,

      200,  200,  200,  201,  201,  201,  202,  202,  202,    0,
        0,  198,  200,    0,    0,  197,  199,  203,  203,  203,
      194,  204,  204,  204,  205,  205,  205,    0,    0,  201,
      206,  206,  206,  208,  208,  208,  198,    0,  200,    0,
        0,  199,    0,  207,  207,  207,  209,  209,  209,  210,
      210,  210,    0,    0,  201,  211,  211,  211,    0,  210,
      212,  212,  212,  206,  207,  213,  213,  213,  214,  214,
      214,  215,  215,  215,  216,  216,  216,  217,  217,  217,
        0,  218,  218,  218,    0,  210,  219,  219,  219,    0,
      207,    0,  215,  218,    0,  217,    0,  215,  220,  220,

      220,  221,  221,  221,  222,  222,  222,    0,  219,  223,
      223,  223,  224,  224,  224,  227,  227,  227,  215,  218,
        0,  217,  215,  225,  225,  225,    0,  221,    0,  222,
      226,  226,  226,    0,  219,    0,  223,  227,    0,  224,
        0,    0,  225,  228,  228,  228,    0,  229,  229,  229,
      226,    0,  221,    0,  222,  228,    0,  230,  230,  230,
        0,  223,    0,  227,  224,  231,  231,  231,  225,  229,
      232,  232,  232,  233,  233,  233,  226,  234,  234,  234,
        0,  228,  230,    0,  232,  235,  235,  235,  231,  234,
        0,    0,    0,    0,    0,  229,    0,    0,    0,    0,

        0,    0,    0,    0,    0,    0,    0,  230,    0,    0,
      232,    0,    0,    0,  231,  234,  237,  237,  237,  237,
      238,  238,  238,  238,  240,  240,  242,    0,  242,  242,
      243,    0,  243,  243,  244,    0,  244,  244,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,
      236,  236,  236,  236,  236,  236,  236,  236,  236,  236,

      236,  236,  236,  236,  236,  236,  236,  236
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "lex.l"
#line 2 "lex.l"
    /* keywords are case insensitive */
    /* we don't need yywrap() function */
    /* we don't need yyunput() function */
    /* we don't need input() function */
#define YY_NO_INPUT 1
    /* enable location */
#include "ast.h"
#include "yacc.tab.h"
#include <iostream>

// automatically update location
#define YY_USER_ACTION \
    yylloc->first_line = yylloc->last_line; \
    yylloc->first_column = yylloc->last_column; \
    for (int i = 0; yytext[i] != '\0'; i++) { \
        if(yytext[i] == '\n') { \
            yylloc->last_line++; \
            yylloc->last_column = 1; \
        } else { \
            yylloc->last_column++; \
        } \
    }

#line 884 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#line 886 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

#define INITIAL 0
#define STATE_COMMENT 1

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

YYSTYPE * yyget_lval ( void );

void yyset_lval ( YYSTYPE * yylval_param  );

       YYLTYPE *yyget_lloc ( void );
    
        void yyset_lloc ( YYLTYPE * yylloc_param  );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param );

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param )
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
        YYSTYPE * yylval;
    
        YYLTYPE * yylloc;
    
    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
#line 47 "lex.l"

#line 49 "lex.l"
    /* block comment */
#line 1124 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 237 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 1339 );

yy_find_action:
		yy_act = yy_accept[yy_current_state];
		if ( yy_act == 0 )
			{ /* have to back up */
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			yy_act = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 50 "lex.l"
{ BEGIN(STATE_COMMENT); }
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 51 "lex.l"
{ BEGIN(INITIAL); }
	YY_BREAK
case 3:
/* rule 3 can match eol */
YY_RULE_SETUP
#line 52 "lex.l"
{ /* ignore the text of the comment */ }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 53 "lex.l"
{ /* ignore *'s that aren't part of */ }
	YY_BREAK
/* single line comment */
case 5:
YY_RULE_SETUP
#line 55 "lex.l"
{ /* ignore single line comment */ }
	YY_BREAK
/* white space and new line */
case 6:
YY_RULE_SETUP
#line 57 "lex.l"
{ /* ignore white space */ }
	YY_BREAK
case 7:
/* rule 7 can match eol */
YY_RULE_SETUP
#line 58 "lex.l"
{ /* ignore new line */ }
	YY_BREAK
/* keywords */
case 8:
YY_RULE_SETUP
#line 60 "lex.l"
{ return SHOW; }
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 61 "lex.l"
{ return TXN_BEGIN; }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 62 "lex.l"
{ return TXN_COMMIT; }
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 63 "lex.l"
{ return TXN_ABORT; }
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 64 "lex.l"
{ return TXN_ROLLBACK; }
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 65 "lex.l"
{ return TABLES; }
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 66 "lex.l"
{ return CREATE; }
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 67 "lex.l"
{ return TABLE; }
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 68 "lex.l"
{ return DROP; }
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 69 "lex.l"
{ return DESC; }
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 70 "lex.l"
{ return INSERT; }
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 71 "lex.l"
{ return INTO; }
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 72 "lex.l"
{ return VALUES; }
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 73 "lex.l"
{ return DELETE; }
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 74 "lex.l"
{ return FROM; }
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 75 "lex.l"
{ return WHERE; }
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 76 "lex.l"
{ return UPDATE; }
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 77 "lex.l"
{ return SET; }
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 78 "lex.l"
{ return SELECT; }
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 79 "lex.l"
{ return INT; }
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 80 "lex.l"
{ return CHAR; }
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 81 "lex.l"
{ return FLOAT; }
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 82 "lex.l"
{ return INDEX; }
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 83 "lex.l"
{ return AND; }
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 84 "lex.l"
{return JOIN;}
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 85 "lex.l"
{ return EXIT; }
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 86 "lex.l"
{ return HELP; }
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 87 "lex.l"
{ return ORDER; }
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 88 "lex.l"
{  return BY;  }
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 89 "lex.l"
{ return ASC; }
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 90 "lex.l"
{ return EXPLAIN; }
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 91 "lex.l"
{ return COUNT; }
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 92 "lex.l"
{ return MIN; }
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 93 "lex.l"
{ return MAX; }
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 94 "lex.l"
{ return AVG; }
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 95 "lex.l"
{ return SUM; }
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 96 "lex.l"
{ return LOAD; }
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 97 "lex.l"
{ return GROUP; }
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 98 "lex.l"
{ return HAVING; }
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 99 "lex.l"
{ return AS; }
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 100 "lex.l"
{ return LIMIT; }
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 101 "lex.l"
{ return ENABLE_NESTLOOP; }
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 102 "lex.l"
{ return ENABLE_SORTMERGE; }
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 103 "lex.l"
{ return AS; }
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 104 "lex.l"
{ return ON; }
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 105 "lex.l"
{ return SEMI; }
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 106 "lex.l"
{ 
    yylval->sv_bool = true;
    return VALUE_BOOL; 
}
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 110 "lex.l"
{
    yylval->sv_bool = false;
    return VALUE_BOOL;
}
	YY_BREAK
/* operators */
case 56:
YY_RULE_SETUP
#line 115 "lex.l"
{ return GEQ; }
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 116 "lex.l"
{ return LEQ; }
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 117 "lex.l"
{ return NEQ; }
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 118 "lex.l"
{ return NEQ; }
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 119 "lex.l"
{ return yytext[0]; }
	YY_BREAK
/* id */
case 61:
YY_RULE_SETUP
#line 121 "lex.l"
{
    yylval->sv_str = yytext;
    return IDENTIFIER;
}
	YY_BREAK
/* literals */
case 62:
YY_RULE_SETUP
#line 126 "lex.l"
{
    yylval->sv_int = atoi(yytext);
    return VALUE_INT;
}
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 130 "lex.l"
{
    yylval->sv_float = atof(yytext);
    return VALUE_FLOAT;
}
	YY_BREAK
case 64:
/* rule 64 can match eol */
YY_RULE_SETUP
#line 134 "lex.l"
{
    yylval->sv_str = std::string(yytext + 1, strlen(yytext) - 2);
    return VALUE_STRING;
}
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 139 "lex.l"
{
            yylval->sv_str = strdup(yytext);
            return PATH_TOKEN;
        }
	YY_BREAK
/* EOF */
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(STATE_COMMENT):
#line 145 "lex.l"
{ return T_EOF; }
	YY_BREAK
/* unexpected char */
case 66:
YY_RULE_SETUP
#line 147 "lex.l"
{ std::cerr << "Lexer Error: unexpected character " << yytext[0] << std::endl; }
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 148 "lex.l"
ECHO;
	YY_BREAK
#line 1553 "/root/dbms3.1/db2025-x1/src/parser/lex.yy.cpp"

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_c_buf_p);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 237 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 237 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 236);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
			fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 148 "lex.l"


