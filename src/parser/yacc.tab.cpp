/* A Bison parser, made by GNU Bison 3.5.1.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2020 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Undocumented macros, especially those whose name start with YY_,
   are private implementation details.  Do not rely on them.  */

/* Identify Bison output.  */
#define YYBISON 1

/* Bison version.  */
#define YYBISON_VERSION "3.5.1"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 2

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1




/* First part of user prologue.  */
#line 1 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"

#include "ast.h"
#include "yacc.tab.h"
#include <iostream>
#include <memory>

int yylex(YYSTYPE *yylval, YYLTYPE *yylloc);

void yyerror(YYLTYPE *locp, const char* s) {
    std::cerr << "Parser Error at line " << locp->first_line << " column " << locp->first_column << ": " << s << std::endl;
}

using namespace ast;

#line 85 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"

# ifndef YY_CAST
#  ifdef __cplusplus
#   define YY_CAST(Type, Val) static_cast<Type> (Val)
#   define YY_REINTERPRET_CAST(Type, Val) reinterpret_cast<Type> (Val)
#  else
#   define YY_CAST(Type, Val) ((Type) (Val))
#   define YY_REINTERPRET_CAST(Type, Val) ((Type) (Val))
#  endif
# endif
# ifndef YY_NULLPTR
#  if defined __cplusplus
#   if 201103L <= __cplusplus
#    define YY_NULLPTR nullptr
#   else
#    define YY_NULLPTR 0
#   endif
#  else
#   define YY_NULLPTR ((void*)0)
#  endif
# endif

/* Enabling verbose error messages.  */
#ifdef YYERROR_VERBOSE
# undef YYERROR_VERBOSE
# define YYERROR_VERBOSE 1
#else
# define YYERROR_VERBOSE 1
#endif

/* Use api.header.include to #include this header
   instead of duplicating it here.  */
#ifndef YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
# define YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 0
#endif
#if YYDEBUG
extern int yydebug;
#endif

/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    SHOW = 258,
    TABLES = 259,
    CREATE = 260,
    TABLE = 261,
    DROP = 262,
    DESC = 263,
    INSERT = 264,
    INTO = 265,
    VALUES = 266,
    DELETE = 267,
    FROM = 268,
    ASC = 269,
    EXPLAIN = 270,
    ORDER = 271,
    BY = 272,
    ON = 273,
    SEMI = 274,
    COUNT = 275,
    MIN = 276,
    MAX = 277,
    AVG = 278,
    SUM = 279,
    GROUP = 280,
    HAVING = 281,
    AS = 282,
    LOAD = 283,
    WHERE = 284,
    UPDATE = 285,
    SET = 286,
    SELECT = 287,
    INT = 288,
    CHAR = 289,
    FLOAT = 290,
    INDEX = 291,
    AND = 292,
    JOIN = 293,
    EXIT = 294,
    HELP = 295,
    TXN_BEGIN = 296,
    TXN_COMMIT = 297,
    TXN_ABORT = 298,
    TXN_ROLLBACK = 299,
    ORDER_BY = 300,
    ENABLE_NESTLOOP = 301,
    ENABLE_SORTMERGE = 302,
    STATIC_CHECKPOINT = 303,
    LIMIT = 304,
    LEQ = 305,
    NEQ = 306,
    GEQ = 307,
    T_EOF = 308,
    IDENTIFIER = 309,
    VALUE_STRING = 310,
    PATH_TOKEN = 311,
    VALUE_INT = 312,
    VALUE_FLOAT = 313,
    VALUE_BOOL = 314
  };
#endif

/* Value type.  */

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int yyparse (void);

#endif /* !YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED  */



#ifdef short
# undef short
#endif

/* On compilers that do not define __PTRDIFF_MAX__ etc., make sure
   <limits.h> and (if available) <stdint.h> are included
   so that the code can choose integer types of a good width.  */

#ifndef __PTRDIFF_MAX__
# include <limits.h> /* INFRINGES ON USER NAME SPACE */
# if defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stdint.h> /* INFRINGES ON USER NAME SPACE */
#  define YY_STDINT_H
# endif
#endif

/* Narrow types that promote to a signed type and that can represent a
   signed or unsigned integer of at least N bits.  In tables they can
   save space and decrease cache pressure.  Promoting to a signed type
   helps avoid bugs in integer arithmetic.  */

#ifdef __INT_LEAST8_MAX__
typedef __INT_LEAST8_TYPE__ yytype_int8;
#elif defined YY_STDINT_H
typedef int_least8_t yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef __INT_LEAST16_MAX__
typedef __INT_LEAST16_TYPE__ yytype_int16;
#elif defined YY_STDINT_H
typedef int_least16_t yytype_int16;
#else
typedef short yytype_int16;
#endif

#if defined __UINT_LEAST8_MAX__ && __UINT_LEAST8_MAX__ <= __INT_MAX__
typedef __UINT_LEAST8_TYPE__ yytype_uint8;
#elif (!defined __UINT_LEAST8_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST8_MAX <= INT_MAX)
typedef uint_least8_t yytype_uint8;
#elif !defined __UINT_LEAST8_MAX__ && UCHAR_MAX <= INT_MAX
typedef unsigned char yytype_uint8;
#else
typedef short yytype_uint8;
#endif

#if defined __UINT_LEAST16_MAX__ && __UINT_LEAST16_MAX__ <= __INT_MAX__
typedef __UINT_LEAST16_TYPE__ yytype_uint16;
#elif (!defined __UINT_LEAST16_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST16_MAX <= INT_MAX)
typedef uint_least16_t yytype_uint16;
#elif !defined __UINT_LEAST16_MAX__ && USHRT_MAX <= INT_MAX
typedef unsigned short yytype_uint16;
#else
typedef int yytype_uint16;
#endif

#ifndef YYPTRDIFF_T
# if defined __PTRDIFF_TYPE__ && defined __PTRDIFF_MAX__
#  define YYPTRDIFF_T __PTRDIFF_TYPE__
#  define YYPTRDIFF_MAXIMUM __PTRDIFF_MAX__
# elif defined PTRDIFF_MAX
#  ifndef ptrdiff_t
#   include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  endif
#  define YYPTRDIFF_T ptrdiff_t
#  define YYPTRDIFF_MAXIMUM PTRDIFF_MAX
# else
#  define YYPTRDIFF_T long
#  define YYPTRDIFF_MAXIMUM LONG_MAX
# endif
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned
# endif
#endif

#define YYSIZE_MAXIMUM                                  \
  YY_CAST (YYPTRDIFF_T,                                 \
           (YYPTRDIFF_MAXIMUM < YY_CAST (YYSIZE_T, -1)  \
            ? YYPTRDIFF_MAXIMUM                         \
            : YY_CAST (YYSIZE_T, -1)))

#define YYSIZEOF(X) YY_CAST (YYPTRDIFF_T, sizeof (X))

/* Stored state numbers (used for stacks). */
typedef yytype_uint8 yy_state_t;

/* State numbers in computations.  */
typedef int yy_state_fast_t;

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif

#ifndef YY_ATTRIBUTE_PURE
# if defined __GNUC__ && 2 < __GNUC__ + (96 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_PURE __attribute__ ((__pure__))
# else
#  define YY_ATTRIBUTE_PURE
# endif
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# if defined __GNUC__ && 2 < __GNUC__ + (7 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_UNUSED __attribute__ ((__unused__))
# else
#  define YY_ATTRIBUTE_UNUSED
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YYUSE(E) ((void) (E))
#else
# define YYUSE(E) /* empty */
#endif

#if defined __GNUC__ && ! defined __ICC && 407 <= __GNUC__ * 100 + __GNUC_MINOR__
/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                            \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")              \
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# define YY_IGNORE_MAYBE_UNINITIALIZED_END      \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif

#if defined __cplusplus && defined __GNUC__ && ! defined __ICC && 6 <= __GNUC__
# define YY_IGNORE_USELESS_CAST_BEGIN                          \
    _Pragma ("GCC diagnostic push")                            \
    _Pragma ("GCC diagnostic ignored \"-Wuseless-cast\"")
# define YY_IGNORE_USELESS_CAST_END            \
    _Pragma ("GCC diagnostic pop")
#endif
#ifndef YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_END
#endif


#define YY_ASSERT(E) ((void) (0 && (E)))

#if ! defined yyoverflow || YYERROR_VERBOSE

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* ! defined yyoverflow || YYERROR_VERBOSE */


#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL \
             && defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yy_state_t yyss_alloc;
  YYSTYPE yyvs_alloc;
  YYLTYPE yyls_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (YYSIZEOF (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (YYSIZEOF (yy_state_t) + YYSIZEOF (YYSTYPE) \
             + YYSIZEOF (YYLTYPE)) \
      + 2 * YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYPTRDIFF_T yynewbytes;                                         \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * YYSIZEOF (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / YYSIZEOF (*yyptr);                        \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, YY_CAST (YYSIZE_T, (Count)) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYPTRDIFF_T yyi;                      \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  61
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   231

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  72
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  44
/* YYNRULES -- Number of rules.  */
#define YYNRULES  115
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  220

#define YYUNDEFTOK  2
#define YYMAXUTOK   314


/* YYTRANSLATE(TOKEN-NUM) -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, with out-of-bounds checking.  */
#define YYTRANSLATE(YYX)                                                \
  (0 <= (YYX) && (YYX) <= YYMAXUTOK ? yytranslate[YYX] : YYUNDEFTOK)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex.  */
static const yytype_int8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
      62,    63,    65,    69,    64,    70,    66,    71,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,    60,
      67,    61,    68,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,    42,    43,    44,
      45,    46,    47,    48,    49,    50,    51,    52,    53,    54,
      55,    56,    57,    58,    59
};

#if YYDEBUG
  /* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_int16 yyrline[] =
{
       0,    69,    69,    74,    79,    84,    92,    93,    94,    95,
      96,    97,    98,   102,   106,   110,   114,   121,   128,   135,
     139,   143,   147,   151,   155,   159,   166,   170,   174,   178,
     267,   271,   278,   287,   291,   298,   302,   309,   316,   320,
     324,   331,   335,   342,   346,   350,   354,   361,   369,   370,
     377,   381,   389,   393,   398,   402,   407,   411,   420,   424,
     428,   436,   440,   444,   448,   452,   456,   466,   470,   477,
     481,   488,   492,   496,   500,   505,   509,   513,   517,   521,
     525,   533,   538,   546,   550,   557,   565,   570,   577,   581,
     585,   593,   597,   607,   612,   618,   630,   643,   659,   663,
     671,   675,   682,   686,   690,   695,   703,   704,   705,   709,
     710,   714,   715,   718,   720,   721
};
#endif

#if YYDEBUG || YYERROR_VERBOSE || 1
/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "$end", "error", "$undefined", "SHOW", "TABLES", "CREATE", "TABLE",
  "DROP", "DESC", "INSERT", "INTO", "VALUES", "DELETE", "FROM", "ASC",
  "EXPLAIN", "ORDER", "BY", "ON", "SEMI", "COUNT", "MIN", "MAX", "AVG",
  "SUM", "GROUP", "HAVING", "AS", "LOAD", "WHERE", "UPDATE", "SET",
  "SELECT", "INT", "CHAR", "FLOAT", "INDEX", "AND", "JOIN", "EXIT", "HELP",
  "TXN_BEGIN", "TXN_COMMIT", "TXN_ABORT", "TXN_ROLLBACK", "ORDER_BY",
  "ENABLE_NESTLOOP", "ENABLE_SORTMERGE", "STATIC_CHECKPOINT", "LIMIT",
  "LEQ", "NEQ", "GEQ", "T_EOF", "IDENTIFIER", "VALUE_STRING", "PATH_TOKEN",
  "VALUE_INT", "VALUE_FLOAT", "VALUE_BOOL", "';'", "'='", "'('", "')'",
  "','", "'*'", "'.'", "'<'", "'>'", "'+'", "'-'", "'/'", "$accept",
  "start", "stmt", "txnStmt", "dbStmt", "setStmt", "ddl", "dml",
  "explainStmt", "loadStmt", "fieldList", "colNameList", "field", "type",
  "valueList", "value", "condition", "optWhereClause", "whereClause",
  "opt_group_by", "opt_having", "havingClause", "havingCondition",
  "aggExpr", "col", "colList", "op", "expr", "setClauses", "setClause",
  "valuexpr", "selector", "select_item", "tableExpression", "tableRef",
  "optAlias", "opt_order_clause", "order_clause", "opt_asc_desc",
  "opt_limit", "set_knob_type", "tbName", "filename", "colName", YY_NULLPTR
};
#endif

# ifdef YYPRINT
/* YYTOKNUM[NUM] -- (External) token number corresponding to the
   (internal) symbol number NUM (which must be that of a token).  */
static const yytype_int16 yytoknum[] =
{
       0,   256,   257,   258,   259,   260,   261,   262,   263,   264,
     265,   266,   267,   268,   269,   270,   271,   272,   273,   274,
     275,   276,   277,   278,   279,   280,   281,   282,   283,   284,
     285,   286,   287,   288,   289,   290,   291,   292,   293,   294,
     295,   296,   297,   298,   299,   300,   301,   302,   303,   304,
     305,   306,   307,   308,   309,   310,   311,   312,   313,   314,
      59,    61,    40,    41,    44,    42,    46,    60,    62,    43,
      45,    47
};
# endif

#define YYPACT_NINF (-154)

#define yypact_value_is_default(Yyn) \
  ((Yyn) == YYPACT_NINF)

#define YYTABLE_NINF (-114)

#define yytable_value_is_error(Yyn) \
  0

  /* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
     STATE-NUM.  */
static const yytype_int16 yypact[] =
{
      77,    -3,     7,    15,   -40,    12,    14,   135,   -22,   -40,
      50,    18,  -154,  -154,  -154,  -154,  -154,  -154,  -154,    47,
      28,  -154,  -154,  -154,  -154,  -154,  -154,  -154,  -154,    93,
     -40,   -40,  -154,   -40,   -40,  -154,  -154,   -40,   -40,    38,
    -154,  -154,  -154,   102,    98,  -154,  -154,    70,    79,    83,
      97,    99,   100,    57,  -154,    -1,  -154,   -10,  -154,    73,
    -154,  -154,  -154,   -40,   104,   106,  -154,   110,   143,   131,
     -40,   119,   105,   -42,   120,   120,   120,   120,   121,  -154,
    -154,   -40,    78,   119,  -154,   119,   119,   119,   115,   120,
    -154,  -154,  -154,   -19,  -154,   117,  -154,   122,   123,   124,
     128,   130,   132,  -154,   -14,  -154,    -1,  -154,  -154,    62,
    -154,   118,    72,  -154,    92,    91,  -154,   145,   129,   119,
    -154,    56,  -154,  -154,  -154,  -154,  -154,  -154,   146,   -40,
     -40,   158,  -154,  -154,   119,  -154,   139,  -154,  -154,  -154,
     119,  -154,  -154,  -154,  -154,  -154,    94,  -154,   120,  -154,
    -154,  -154,  -154,  -154,  -154,  -154,  -154,  -154,  -154,    56,
    -154,    17,  -154,   129,  -154,   -40,   184,  -154,   186,   178,
    -154,   148,  -154,  -154,    91,  -154,  -154,  -154,    56,   189,
     120,   120,    78,   192,   147,  -154,  -154,   120,   145,  -154,
     149,   172,  -154,   129,   129,   194,   163,  -154,   145,   120,
      78,    56,    36,   120,   157,  -154,  -154,  -154,  -154,  -154,
    -154,    61,   151,  -154,  -154,  -154,  -154,   120,    61,  -154
};

  /* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
     Performed when YYTABLE does not specify something else to do.  Zero
     means the default is an error.  */
static const yytype_int8 yydefact[] =
{
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     4,     3,    13,    14,    15,    16,     5,     0,
       0,     9,     6,    10,     7,     8,    11,    12,    17,     0,
       0,     0,    25,     0,     0,   113,    21,     0,     0,     0,
      31,    30,   114,     0,     0,   111,   112,     0,     0,     0,
       0,     0,     0,   115,    88,     0,    91,     0,    89,     0,
      68,     1,     2,     0,     0,     0,    20,     0,     0,    48,
       0,     0,     0,     0,     0,     0,     0,     0,     0,   101,
      92,     0,     0,     0,    24,     0,     0,     0,     0,     0,
      27,    32,   115,    48,    83,     0,    18,     0,     0,     0,
       0,     0,     0,   100,    48,    93,    98,    90,    67,     0,
      33,     0,     0,    35,     0,     0,    50,    49,     0,     0,
      28,     0,    61,    62,    66,    65,    64,    63,     0,     0,
       0,    53,    99,    19,     0,    38,     0,    40,    37,    22,
       0,    23,    45,    43,    44,    46,     0,    41,     0,    79,
      78,    80,    71,    76,    72,    73,    74,    75,    77,     0,
      84,    81,    82,     0,    85,     0,    96,    94,     0,    55,
      34,     0,    36,    26,     0,    51,    81,    47,     0,     0,
       0,     0,     0,   103,     0,    42,    86,     0,    95,    69,
      52,    54,    56,     0,     0,     0,   110,    39,    97,     0,
       0,     0,     0,     0,     0,    29,    70,    57,    58,    60,
      59,   108,   102,   109,   107,   106,   104,     0,   108,   105
};

  /* YYPGOTO[NTERM-NUM].  */
static const yytype_int16 yypgoto[] =
{
    -154,  -154,  -154,  -154,  -154,  -154,   209,   210,  -154,  -154,
    -154,   133,    84,  -154,  -154,  -104,    71,   -17,  -150,  -154,
    -154,  -154,    21,   -78,   -11,  -154,   -60,  -153,  -154,   103,
    -154,  -154,   141,  -154,  -121,   125,  -154,  -154,     6,  -154,
    -154,    -2,  -154,   -67
};

  /* YYDEFGOTO[NTERM-NUM].  */
static const yytype_int16 yydefgoto[] =
{
      -1,    19,    20,    21,    22,    23,    24,    25,    26,    27,
     109,   112,   110,   138,   146,   176,   116,    90,   117,   169,
     183,   191,   192,    55,   162,   190,   159,   163,    93,    94,
     164,    57,    58,   104,   105,    80,   196,   212,   216,   205,
      47,    59,    43,    60
};

  /* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
     positive, shift that token.  If negative, reduce the rule whose
     number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int16 yytable[] =
{
      56,    28,    36,    81,    95,   128,   177,    44,   166,   167,
      89,   147,    53,    30,    35,    89,   108,   161,   111,   113,
     113,    33,    37,    97,   129,   186,    78,    38,    64,    65,
     188,    66,    67,    29,    42,    68,    69,   198,    48,    49,
      50,    51,    52,    31,   179,   119,   -87,    61,   208,   210,
     130,    34,    95,    79,    82,    32,    48,    49,    50,    51,
      52,    84,    98,    99,   100,   101,   102,   111,    91,   214,
     185,    56,    53,   172,    29,   215,   120,   -87,   118,   106,
       1,   -87,     2,    54,     3,     4,     5,   131,    62,     6,
      53,   142,     7,   143,   144,   145,    45,    46,    48,    49,
      50,    51,    52,   178,   193,     8,    63,     9,    10,    11,
      53,   142,    70,   143,   144,   145,    12,    13,    14,    15,
      16,    17,   193,  -113,   209,   133,   134,   106,   106,    71,
      18,    72,    53,   201,   202,   139,   140,   118,    39,    83,
       2,    73,     3,     4,     5,    74,   142,     6,   143,   144,
     145,   135,   136,   137,    88,   141,   140,   173,   174,    75,
      89,    76,    77,   106,    96,     9,    85,    11,    86,   118,
     189,   194,    87,    92,    53,   103,   118,   115,   121,   149,
     150,   151,   148,   168,   165,   122,   123,   124,   206,   194,
     152,   125,   211,   126,   153,   127,   154,   155,   156,   157,
     158,   171,   180,   181,   182,   184,   218,   187,   195,   200,
     197,   203,   204,   199,   213,   217,    40,    41,   170,   175,
     114,   207,   160,   107,   219,     0,     0,     0,     0,     0,
       0,   132
};

static const yytype_int16 yycheck[] =
{
      11,     4,     4,    13,    71,    19,   159,     9,   129,   130,
      29,   115,    54,     6,    54,    29,    83,   121,    85,    86,
      87,     6,    10,    65,    38,   178,    27,    13,    30,    31,
     180,    33,    34,    36,    56,    37,    38,   187,    20,    21,
      22,    23,    24,    36,   165,    64,    29,     0,   201,   202,
      64,    36,   119,    54,    64,    48,    20,    21,    22,    23,
      24,    63,    73,    74,    75,    76,    77,   134,    70,     8,
     174,    82,    54,   140,    36,    14,    93,    60,    89,    81,
       3,    64,     5,    65,     7,     8,     9,   104,    60,    12,
      54,    55,    15,    57,    58,    59,    46,    47,    20,    21,
      22,    23,    24,   163,   182,    28,    13,    30,    31,    32,
      54,    55,    10,    57,    58,    59,    39,    40,    41,    42,
      43,    44,   200,    66,   202,    63,    64,   129,   130,    31,
      53,    61,    54,   193,   194,    63,    64,   148,     3,    66,
       5,    62,     7,     8,     9,    62,    55,    12,    57,    58,
      59,    33,    34,    35,    11,    63,    64,    63,    64,    62,
      29,    62,    62,   165,    59,    30,    62,    32,    62,   180,
     181,   182,    62,    54,    54,    54,   187,    62,    61,    50,
      51,    52,    37,    25,    38,    63,    63,    63,   199,   200,
      61,    63,   203,    63,    65,    63,    67,    68,    69,    70,
      71,    62,    18,    17,    26,    57,   217,    18,    16,    37,
      63,    17,    49,    64,    57,    64,     7,     7,   134,   148,
      87,   200,   119,    82,   218,    -1,    -1,    -1,    -1,    -1,
      -1,   106
};

  /* YYSTOS[STATE-NUM] -- The (internal number of the) accessing
     symbol of state STATE-NUM.  */
static const yytype_int8 yystos[] =
{
       0,     3,     5,     7,     8,     9,    12,    15,    28,    30,
      31,    32,    39,    40,    41,    42,    43,    44,    53,    73,
      74,    75,    76,    77,    78,    79,    80,    81,     4,    36,
       6,    36,    48,     6,    36,    54,   113,    10,    13,     3,
      78,    79,    56,   114,   113,    46,    47,   112,    20,    21,
      22,    23,    24,    54,    65,    95,    96,   103,   104,   113,
     115,     0,    60,    13,   113,   113,   113,   113,   113,   113,
      10,    31,    61,    62,    62,    62,    62,    62,    27,    54,
     107,    13,    64,    66,   113,    62,    62,    62,    11,    29,
      89,   113,    54,   100,   101,   115,    59,    65,    96,    96,
      96,    96,    96,    54,   105,   106,   113,   104,   115,    82,
      84,   115,    83,   115,    83,    62,    88,    90,    96,    64,
      89,    61,    63,    63,    63,    63,    63,    63,    19,    38,
      64,    89,   107,    63,    64,    33,    34,    35,    85,    63,
      64,    63,    55,    57,    58,    59,    86,    87,    37,    50,
      51,    52,    61,    65,    67,    68,    69,    70,    71,    98,
     101,    87,    96,    99,   102,    38,   106,   106,    25,    91,
      84,    62,   115,    63,    64,    88,    87,    99,    98,   106,
      18,    17,    26,    92,    57,    87,    99,    18,    90,    96,
      97,    93,    94,    95,    96,    16,   108,    63,    90,    64,
      37,    98,    98,    17,    49,   111,    96,    94,    99,    95,
      99,    96,   109,    57,     8,    14,   110,    64,    96,   110
};

  /* YYR1[YYN] -- Symbol number of symbol that rule YYN derives.  */
static const yytype_int8 yyr1[] =
{
       0,    72,    73,    73,    73,    73,    74,    74,    74,    74,
      74,    74,    74,    75,    75,    75,    75,    76,    77,    78,
      78,    78,    78,    78,    78,    78,    79,    79,    79,    79,
      80,    80,    81,    82,    82,    83,    83,    84,    85,    85,
      85,    86,    86,    87,    87,    87,    87,    88,    89,    89,
      90,    90,    91,    91,    92,    92,    93,    93,    94,    94,
      94,    95,    95,    95,    95,    95,    95,    96,    96,    97,
      97,    98,    98,    98,    98,    98,    98,    98,    98,    98,
      98,    99,    99,   100,   100,   101,   102,   102,   103,   103,
     103,   104,   104,   105,   105,   105,   105,   105,   106,   106,
     107,   107,   108,   108,   109,   109,   110,   110,   110,   111,
     111,   112,   112,   113,   114,   115
};

  /* YYR2[YYN] -- Number of symbols on the right hand side of rule YYN.  */
static const yytype_int8 yyr2[] =
{
       0,     2,     2,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     2,     4,     6,
       3,     2,     6,     6,     4,     2,     7,     4,     5,     9,
       2,     2,     4,     1,     3,     1,     3,     2,     1,     4,
       1,     1,     3,     1,     1,     1,     1,     3,     0,     2,
       1,     3,     3,     0,     2,     0,     1,     3,     3,     3,
       3,     4,     4,     4,     4,     4,     4,     3,     1,     1,
       3,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     3,     3,     3,     1,     1,     1,
       3,     1,     2,     1,     3,     5,     3,     6,     1,     2,
       2,     1,     3,     0,     2,     4,     1,     1,     0,     2,
       0,     1,     1,     1,     1,     1
};


#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)
#define YYEMPTY         (-2)
#define YYEOF           0

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                    \
  do                                                              \
    if (yychar == YYEMPTY)                                        \
      {                                                           \
        yychar = (Token);                                         \
        yylval = (Value);                                         \
        YYPOPSTACK (yylen);                                       \
        yystate = *yyssp;                                         \
        goto yybackup;                                            \
      }                                                           \
    else                                                          \
      {                                                           \
        yyerror (&yylloc, YY_("syntax error: cannot back up")); \
        YYERROR;                                                  \
      }                                                           \
  while (0)

/* Error token number */
#define YYTERROR        1
#define YYERRCODE       256


/* YYLLOC_DEFAULT -- Set CURRENT to span from RHS[1] to RHS[N].
   If N is 0, then set CURRENT to the empty location which ends
   the previous symbol: RHS[0] (always defined).  */

#ifndef YYLLOC_DEFAULT
# define YYLLOC_DEFAULT(Current, Rhs, N)                                \
    do                                                                  \
      if (N)                                                            \
        {                                                               \
          (Current).first_line   = YYRHSLOC (Rhs, 1).first_line;        \
          (Current).first_column = YYRHSLOC (Rhs, 1).first_column;      \
          (Current).last_line    = YYRHSLOC (Rhs, N).last_line;         \
          (Current).last_column  = YYRHSLOC (Rhs, N).last_column;       \
        }                                                               \
      else                                                              \
        {                                                               \
          (Current).first_line   = (Current).last_line   =              \
            YYRHSLOC (Rhs, 0).last_line;                                \
          (Current).first_column = (Current).last_column =              \
            YYRHSLOC (Rhs, 0).last_column;                              \
        }                                                               \
    while (0)
#endif

#define YYRHSLOC(Rhs, K) ((Rhs)[K])


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)


/* YY_LOCATION_PRINT -- Print the location on the stream.
   This macro was not mandated originally: define only if we know
   we won't break user code: when these are the locations we know.  */

#ifndef YY_LOCATION_PRINT
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL

/* Print *YYLOCP on YYO.  Private, do not rely on its existence. */

YY_ATTRIBUTE_UNUSED
static int
yy_location_print_ (FILE *yyo, YYLTYPE const * const yylocp)
{
  int res = 0;
  int end_col = 0 != yylocp->last_column ? yylocp->last_column - 1 : 0;
  if (0 <= yylocp->first_line)
    {
      res += YYFPRINTF (yyo, "%d", yylocp->first_line);
      if (0 <= yylocp->first_column)
        res += YYFPRINTF (yyo, ".%d", yylocp->first_column);
    }
  if (0 <= yylocp->last_line)
    {
      if (yylocp->first_line < yylocp->last_line)
        {
          res += YYFPRINTF (yyo, "-%d", yylocp->last_line);
          if (0 <= end_col)
            res += YYFPRINTF (yyo, ".%d", end_col);
        }
      else if (0 <= end_col && yylocp->first_column < end_col)
        res += YYFPRINTF (yyo, "-%d", end_col);
    }
  return res;
 }

#  define YY_LOCATION_PRINT(File, Loc)          \
  yy_location_print_ (File, &(Loc))

# else
#  define YY_LOCATION_PRINT(File, Loc) ((void) 0)
# endif
#endif


# define YY_SYMBOL_PRINT(Title, Type, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Type, Value, Location); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*-----------------------------------.
| Print this symbol's value on YYO.  |
`-----------------------------------*/

static void
yy_symbol_value_print (FILE *yyo, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp)
{
  FILE *yyoutput = yyo;
  YYUSE (yyoutput);
  YYUSE (yylocationp);
  if (!yyvaluep)
    return;
# ifdef YYPRINT
  if (yytype < YYNTOKENS)
    YYPRINT (yyo, yytoknum[yytype], *yyvaluep);
# endif
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YYUSE (yytype);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}


/*---------------------------.
| Print this symbol on YYO.  |
`---------------------------*/

static void
yy_symbol_print (FILE *yyo, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp)
{
  YYFPRINTF (yyo, "%s %s (",
             yytype < YYNTOKENS ? "token" : "nterm", yytname[yytype]);

  YY_LOCATION_PRINT (yyo, *yylocationp);
  YYFPRINTF (yyo, ": ");
  yy_symbol_value_print (yyo, yytype, yyvaluep, yylocationp);
  YYFPRINTF (yyo, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yy_state_t *yybottom, yy_state_t *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yy_state_t *yyssp, YYSTYPE *yyvsp, YYLTYPE *yylsp, int yyrule)
{
  int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %d):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       yystos[+yyssp[yyi + 1 - yynrhs]],
                       &yyvsp[(yyi + 1) - (yynrhs)]
                       , &(yylsp[(yyi + 1) - (yynrhs)])                       );
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, yylsp, Rule); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args)
# define YY_SYMBOL_PRINT(Title, Type, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif


#if YYERROR_VERBOSE

# ifndef yystrlen
#  if defined __GLIBC__ && defined _STRING_H
#   define yystrlen(S) (YY_CAST (YYPTRDIFF_T, strlen (S)))
#  else
/* Return the length of YYSTR.  */
static YYPTRDIFF_T
yystrlen (const char *yystr)
{
  YYPTRDIFF_T yylen;
  for (yylen = 0; yystr[yylen]; yylen++)
    continue;
  return yylen;
}
#  endif
# endif

# ifndef yystpcpy
#  if defined __GLIBC__ && defined _STRING_H && defined _GNU_SOURCE
#   define yystpcpy stpcpy
#  else
/* Copy YYSRC to YYDEST, returning the address of the terminating '\0' in
   YYDEST.  */
static char *
yystpcpy (char *yydest, const char *yysrc)
{
  char *yyd = yydest;
  const char *yys = yysrc;

  while ((*yyd++ = *yys++) != '\0')
    continue;

  return yyd - 1;
}
#  endif
# endif

# ifndef yytnamerr
/* Copy to YYRES the contents of YYSTR after stripping away unnecessary
   quotes and backslashes, so that it's suitable for yyerror.  The
   heuristic is that double-quoting is unnecessary unless the string
   contains an apostrophe, a comma, or backslash (other than
   backslash-backslash).  YYSTR is taken from yytname.  If YYRES is
   null, do not copy; instead, return the length of what the result
   would have been.  */
static YYPTRDIFF_T
yytnamerr (char *yyres, const char *yystr)
{
  if (*yystr == '"')
    {
      YYPTRDIFF_T yyn = 0;
      char const *yyp = yystr;

      for (;;)
        switch (*++yyp)
          {
          case '\'':
          case ',':
            goto do_not_strip_quotes;

          case '\\':
            if (*++yyp != '\\')
              goto do_not_strip_quotes;
            else
              goto append;

          append:
          default:
            if (yyres)
              yyres[yyn] = *yyp;
            yyn++;
            break;

          case '"':
            if (yyres)
              yyres[yyn] = '\0';
            return yyn;
          }
    do_not_strip_quotes: ;
    }

  if (yyres)
    return yystpcpy (yyres, yystr) - yyres;
  else
    return yystrlen (yystr);
}
# endif

/* Copy into *YYMSG, which is of size *YYMSG_ALLOC, an error message
   about the unexpected token YYTOKEN for the state stack whose top is
   YYSSP.

   Return 0 if *YYMSG was successfully written.  Return 1 if *YYMSG is
   not large enough to hold the message.  In that case, also set
   *YYMSG_ALLOC to the required number of bytes.  Return 2 if the
   required number of bytes is too large to store.  */
static int
yysyntax_error (YYPTRDIFF_T *yymsg_alloc, char **yymsg,
                yy_state_t *yyssp, int yytoken)
{
  enum { YYERROR_VERBOSE_ARGS_MAXIMUM = 5 };
  /* Internationalized format string. */
  const char *yyformat = YY_NULLPTR;
  /* Arguments of yyformat: reported tokens (one for the "unexpected",
     one per "expected"). */
  char const *yyarg[YYERROR_VERBOSE_ARGS_MAXIMUM];
  /* Actual size of YYARG. */
  int yycount = 0;
  /* Cumulated lengths of YYARG.  */
  YYPTRDIFF_T yysize = 0;

  /* There are many possibilities here to consider:
     - If this state is a consistent state with a default action, then
       the only way this function was invoked is if the default action
       is an error action.  In that case, don't check for expected
       tokens because there are none.
     - The only way there can be no lookahead present (in yychar) is if
       this state is a consistent state with a default action.  Thus,
       detecting the absence of a lookahead is sufficient to determine
       that there is no unexpected or expected token to report.  In that
       case, just report a simple "syntax error".
     - Don't assume there isn't a lookahead just because this state is a
       consistent state with a default action.  There might have been a
       previous inconsistent state, consistent state with a non-default
       action, or user semantic action that manipulated yychar.
     - Of course, the expected token list depends on states to have
       correct lookahead information, and it depends on the parser not
       to perform extra reductions after fetching a lookahead from the
       scanner and before detecting a syntax error.  Thus, state merging
       (from LALR or IELR) and default reductions corrupt the expected
       token list.  However, the list is correct for canonical LR with
       one exception: it will still contain any token that will not be
       accepted due to an error action in a later state.
  */
  if (yytoken != YYEMPTY)
    {
      int yyn = yypact[+*yyssp];
      YYPTRDIFF_T yysize0 = yytnamerr (YY_NULLPTR, yytname[yytoken]);
      yysize = yysize0;
      yyarg[yycount++] = yytname[yytoken];
      if (!yypact_value_is_default (yyn))
        {
          /* Start YYX at -YYN if negative to avoid negative indexes in
             YYCHECK.  In other words, skip the first -YYN actions for
             this state because they are default actions.  */
          int yyxbegin = yyn < 0 ? -yyn : 0;
          /* Stay within bounds of both yycheck and yytname.  */
          int yychecklim = YYLAST - yyn + 1;
          int yyxend = yychecklim < YYNTOKENS ? yychecklim : YYNTOKENS;
          int yyx;

          for (yyx = yyxbegin; yyx < yyxend; ++yyx)
            if (yycheck[yyx + yyn] == yyx && yyx != YYTERROR
                && !yytable_value_is_error (yytable[yyx + yyn]))
              {
                if (yycount == YYERROR_VERBOSE_ARGS_MAXIMUM)
                  {
                    yycount = 1;
                    yysize = yysize0;
                    break;
                  }
                yyarg[yycount++] = yytname[yyx];
                {
                  YYPTRDIFF_T yysize1
                    = yysize + yytnamerr (YY_NULLPTR, yytname[yyx]);
                  if (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM)
                    yysize = yysize1;
                  else
                    return 2;
                }
              }
        }
    }

  switch (yycount)
    {
# define YYCASE_(N, S)                      \
      case N:                               \
        yyformat = S;                       \
      break
    default: /* Avoid compiler warnings. */
      YYCASE_(0, YY_("syntax error"));
      YYCASE_(1, YY_("syntax error, unexpected %s"));
      YYCASE_(2, YY_("syntax error, unexpected %s, expecting %s"));
      YYCASE_(3, YY_("syntax error, unexpected %s, expecting %s or %s"));
      YYCASE_(4, YY_("syntax error, unexpected %s, expecting %s or %s or %s"));
      YYCASE_(5, YY_("syntax error, unexpected %s, expecting %s or %s or %s or %s"));
# undef YYCASE_
    }

  {
    /* Don't count the "%s"s in the final size, but reserve room for
       the terminator.  */
    YYPTRDIFF_T yysize1 = yysize + (yystrlen (yyformat) - 2 * yycount) + 1;
    if (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM)
      yysize = yysize1;
    else
      return 2;
  }

  if (*yymsg_alloc < yysize)
    {
      *yymsg_alloc = 2 * yysize;
      if (! (yysize <= *yymsg_alloc
             && *yymsg_alloc <= YYSTACK_ALLOC_MAXIMUM))
        *yymsg_alloc = YYSTACK_ALLOC_MAXIMUM;
      return 1;
    }

  /* Avoid sprintf, as that infringes on the user's name space.
     Don't have undefined behavior even if the translation
     produced a string with the wrong number of "%s"s.  */
  {
    char *yyp = *yymsg;
    int yyi = 0;
    while ((*yyp = *yyformat) != '\0')
      if (*yyp == '%' && yyformat[1] == 's' && yyi < yycount)
        {
          yyp += yytnamerr (yyp, yyarg[yyi++]);
          yyformat += 2;
        }
      else
        {
          ++yyp;
          ++yyformat;
        }
  }
  return 0;
}
#endif /* YYERROR_VERBOSE */

/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg, int yytype, YYSTYPE *yyvaluep, YYLTYPE *yylocationp)
{
  YYUSE (yyvaluep);
  YYUSE (yylocationp);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yytype, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YYUSE (yytype);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}




/*----------.
| yyparse.  |
`----------*/

int
yyparse (void)
{
/* The lookahead symbol.  */
int yychar;


/* The semantic value of the lookahead symbol.  */
/* Default value used for initialization, for pacifying older GCCs
   or non-GCC compilers.  */
YY_INITIAL_VALUE (static YYSTYPE yyval_default;)
YYSTYPE yylval YY_INITIAL_VALUE (= yyval_default);

/* Location data for the lookahead symbol.  */
static YYLTYPE yyloc_default
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL
  = { 1, 1, 1, 1 }
# endif
;
YYLTYPE yylloc = yyloc_default;

    /* Number of syntax errors so far.  */
    int yynerrs;

    yy_state_fast_t yystate;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus;

    /* The stacks and their tools:
       'yyss': related to states.
       'yyvs': related to semantic values.
       'yyls': related to locations.

       Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* The state stack.  */
    yy_state_t yyssa[YYINITDEPTH];
    yy_state_t *yyss;
    yy_state_t *yyssp;

    /* The semantic value stack.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs;
    YYSTYPE *yyvsp;

    /* The location stack.  */
    YYLTYPE yylsa[YYINITDEPTH];
    YYLTYPE *yyls;
    YYLTYPE *yylsp;

    /* The locations where the error started and ended.  */
    YYLTYPE yyerror_range[3];

    YYPTRDIFF_T yystacksize;

  int yyn;
  int yyresult;
  /* Lookahead token as an internal (translated) token number.  */
  int yytoken = 0;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;
  YYLTYPE yyloc;

#if YYERROR_VERBOSE
  /* Buffer for error messages, and its allocated size.  */
  char yymsgbuf[128];
  char *yymsg = yymsgbuf;
  YYPTRDIFF_T yymsg_alloc = sizeof yymsgbuf;
#endif

#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N), yylsp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  yyssp = yyss = yyssa;
  yyvsp = yyvs = yyvsa;
  yylsp = yyls = yylsa;
  yystacksize = YYINITDEPTH;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yystate = 0;
  yyerrstatus = 0;
  yynerrs = 0;
  yychar = YYEMPTY; /* Cause a token to be read.  */
  yylsp[0] = yylloc;
  goto yysetstate;


/*------------------------------------------------------------.
| yynewstate -- push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;


/*--------------------------------------------------------------------.
| yysetstate -- set current state (the top of the stack) to yystate.  |
`--------------------------------------------------------------------*/
yysetstate:
  YYDPRINTF ((stderr, "Entering state %d\n", yystate));
  YY_ASSERT (0 <= yystate && yystate < YYNSTATES);
  YY_IGNORE_USELESS_CAST_BEGIN
  *yyssp = YY_CAST (yy_state_t, yystate);
  YY_IGNORE_USELESS_CAST_END

  if (yyss + yystacksize - 1 <= yyssp)
#if !defined yyoverflow && !defined YYSTACK_RELOCATE
    goto yyexhaustedlab;
#else
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYPTRDIFF_T yysize = yyssp - yyss + 1;

# if defined yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        yy_state_t *yyss1 = yyss;
        YYSTYPE *yyvs1 = yyvs;
        YYLTYPE *yyls1 = yyls;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * YYSIZEOF (*yyssp),
                    &yyvs1, yysize * YYSIZEOF (*yyvsp),
                    &yyls1, yysize * YYSIZEOF (*yylsp),
                    &yystacksize);
        yyss = yyss1;
        yyvs = yyvs1;
        yyls = yyls1;
      }
# else /* defined YYSTACK_RELOCATE */
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        goto yyexhaustedlab;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yy_state_t *yyss1 = yyss;
        union yyalloc *yyptr =
          YY_CAST (union yyalloc *,
                   YYSTACK_ALLOC (YY_CAST (YYSIZE_T, YYSTACK_BYTES (yystacksize))));
        if (! yyptr)
          goto yyexhaustedlab;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
        YYSTACK_RELOCATE (yyls_alloc, yyls);
# undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;
      yylsp = yyls + yysize - 1;

      YY_IGNORE_USELESS_CAST_BEGIN
      YYDPRINTF ((stderr, "Stack size increased to %ld\n",
                  YY_CAST (long, yystacksize)));
      YY_IGNORE_USELESS_CAST_END

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }
#endif /* !defined yyoverflow && !defined YYSTACK_RELOCATE */

  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;


/*-----------.
| yybackup.  |
`-----------*/
yybackup:
  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either YYEMPTY or YYEOF or a valid lookahead symbol.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token: "));
      yychar = yylex (&yylval, &yylloc);
    }

  if (yychar <= YYEOF)
    {
      yychar = yytoken = YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);
  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END
  *++yylsp = yylloc;

  /* Discard the shifted token.  */
  yychar = YYEMPTY;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];

  /* Default location. */
  YYLLOC_DEFAULT (yyloc, (yylsp - yylen), yylen);
  yyerror_range[1] = yyloc;
  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
  case 2:
#line 70 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = (yyvsp[-1].sv_node);
        YYACCEPT;
    }
#line 1648 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 3:
#line 75 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = std::make_shared<Help>();
        YYACCEPT;
    }
#line 1657 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 4:
#line 80 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
#line 1666 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 5:
#line 85 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        parse_tree = nullptr;
        YYACCEPT;
    }
#line 1675 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 13:
#line 103 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnBegin>();
    }
#line 1683 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 14:
#line 107 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnCommit>();
    }
#line 1691 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 15:
#line 111 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnAbort>();
    }
#line 1699 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 16:
#line 115 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<TxnRollback>();
    }
#line 1707 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 17:
#line 122 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ShowTables>();
    }
#line 1715 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 18:
#line 129 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<SetStmt>((yyvsp[-2].sv_setKnobType), (yyvsp[0].sv_bool));
    }
#line 1723 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 19:
#line 136 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateTable>((yyvsp[-3].sv_str), (yyvsp[-1].sv_fields));
    }
#line 1731 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 20:
#line 140 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DropTable>((yyvsp[0].sv_str));
    }
#line 1739 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 21:
#line 144 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DescTable>((yyvsp[0].sv_str));
    }
#line 1747 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 22:
#line 148 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateIndex>((yyvsp[-3].sv_str), (yyvsp[-1].sv_strs));
    }
#line 1755 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 23:
#line 152 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DropIndex>((yyvsp[-3].sv_str), (yyvsp[-1].sv_strs));
    }
#line 1763 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 24:
#line 156 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ShowIndex>((yyvsp[0].sv_str));
    }
#line 1771 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 25:
#line 160 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<CreateStaticCheckpoint>();
    }
#line 1779 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 26:
#line 167 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<InsertStmt>((yyvsp[-4].sv_str), (yyvsp[-1].sv_vals));
    }
#line 1787 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 27:
#line 171 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<DeleteStmt>((yyvsp[-1].sv_str), (yyvsp[0].sv_conds));
    }
#line 1795 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 28:
#line 175 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<UpdateStmt>((yyvsp[-3].sv_str), (yyvsp[-1].sv_set_clauses), (yyvsp[0].sv_conds));
    }
#line 1803 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 29:
#line 179 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {


        // 检查是否包含聚合函数
        std::vector<std::shared_ptr<AggExpr>> aggexprs;
        std::vector<std::shared_ptr<Col>> cols;
        std::vector<std::shared_ptr<Col>> select_cols; // 只包含普通列，不包括聚合函数别名
        bool has_aggregate = false;
        for (const auto& item : (yyvsp[-7].sv_select_items)) {
            if (item->agg_expr) {
                // 如果聚合函数没有别名，生成默认列名
                std::string col_name = item->agg_expr->alias;
                if (col_name.empty()) {
                    // 生成默认列名，如 MAX(id), COUNT(*) 等
                    std::string func_name;
                    switch (item->agg_expr->type) {
                        case AGG_COUNT: func_name = "COUNT"; break;
                        case AGG_SUM: func_name = "SUM"; break;
                        case AGG_AVG: func_name = "AVG"; break;
                        case AGG_MAX: func_name = "MAX"; break;
                        case AGG_MIN: func_name = "MIN"; break;
                        default: func_name = "UNKNOWN"; break;
                    }

                    if (item->agg_expr->col) {
                        col_name = func_name + "(" + item->agg_expr->col->col_name + ")";
                    } else {
                        col_name = func_name + "(*)";
                    }
                    item->agg_expr->alias = col_name;  // 更新别名
                }

                std::shared_ptr<Col> col = std::make_shared<Col>("", col_name);
                aggexprs.push_back(item->agg_expr);
                cols.push_back(col);
                has_aggregate = true;
            }
            else
            {
                cols.push_back(item->column);
                select_cols.push_back(item->column); // 只添加普通列到select_cols
            }
        }

        if (has_aggregate || !(yyvsp[-3].sv_group_by).empty()) {
            // 构建聚合查询（有聚合函数或有GROUP BY子句）
            std::vector<std::string> Agg_tables;
            for(auto& aggtab : (yyvsp[-5].sv_table_expr).tables)
            {
                Agg_tables.push_back(aggtab.first);
            }
            (yyval.sv_node) = std::make_shared<GroupByStmt>(
                select_cols, // 使用select_cols而不是cols
                aggexprs,
                Agg_tables,
                std::vector<std::shared_ptr<JoinExpr>>{},
                (yyvsp[-3].sv_group_by),
                (yyvsp[-2].sv_having),
                (yyvsp[-4].sv_conds),
                (yyvsp[-1].sv_orderby),
                (yyvsp[0].sv_int)  // 添加LIMIT参数
            );
        }
        else
        {
            // 检查是否包含SEMI JOIN
            bool has_semi_join = false;
            for (const auto& join : (yyvsp[-5].sv_table_expr).joins) {
                if (join->type == SEMI_JOIN) {
                    has_semi_join = true;
                    break;
                }
            }

            if (has_semi_join) {
                (yyval.sv_node) = std::make_shared<SemijoinStmt>(cols, (yyvsp[-5].sv_table_expr).tables, (yyvsp[-5].sv_table_expr).joins, (yyvsp[-4].sv_conds));
            }
            else {
                // 普通查询
                (yyval.sv_node) = std::make_shared<SelectStmt>(cols, (yyvsp[-5].sv_table_expr).tables, (yyvsp[-5].sv_table_expr).joins, (yyvsp[-4].sv_conds), (yyvsp[-1].sv_orderby), (yyvsp[0].sv_int));  // 添加LIMIT参数
            }
        }
        

    }
#line 1893 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 30:
#line 268 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ExplainStmt>((yyvsp[0].sv_node));
    }
#line 1901 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 31:
#line 272 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_node) = std::make_shared<ExplainStmt>((yyvsp[0].sv_node));
    }
#line 1909 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 32:
#line 279 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("111\n");
        (yyval.sv_node) = std::make_shared<LoadStmt>((yyvsp[0].sv_str), (yyvsp[-2].sv_str));
        printf("111\n");
    }
#line 1919 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 33:
#line 288 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_fields) = std::vector<std::shared_ptr<Field>>{(yyvsp[0].sv_field)};
    }
#line 1927 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 34:
#line 292 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_fields).push_back((yyvsp[0].sv_field));
    }
#line 1935 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 35:
#line 299 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strs) = std::vector<std::string>{(yyvsp[0].sv_str)};
    }
#line 1943 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 36:
#line 303 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strs).push_back((yyvsp[0].sv_str));
    }
#line 1951 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 37:
#line 310 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_field) = std::make_shared<ColDef>((yyvsp[-1].sv_str), (yyvsp[0].sv_type_len));
    }
#line 1959 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 38:
#line 317 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_INT, sizeof(int));
    }
#line 1967 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 39:
#line 321 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_STRING, (yyvsp[-1].sv_int));
    }
#line 1975 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 40:
#line 325 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_type_len) = std::make_shared<TypeLen>(SV_TYPE_FLOAT, sizeof(float));
    }
#line 1983 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 41:
#line 332 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_vals) = std::vector<std::shared_ptr<Value>>{(yyvsp[0].sv_val)};
    }
#line 1991 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 42:
#line 336 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_vals).push_back((yyvsp[0].sv_val));
    }
#line 1999 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 43:
#line 343 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<IntLit>((yyvsp[0].sv_int));
    }
#line 2007 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 44:
#line 347 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<FloatLit>((yyvsp[0].sv_float));
    }
#line 2015 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 45:
#line 351 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<StringLit>((yyvsp[0].sv_str));
    }
#line 2023 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 46:
#line 355 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_val) = std::make_shared<BoolLit>((yyvsp[0].sv_bool));
    }
#line 2031 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 47:
#line 362 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cond) = std::make_shared<BinaryExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2039 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 48:
#line 369 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                      { /* ignore*/ }
#line 2045 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 49:
#line 371 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds) = (yyvsp[0].sv_conds);
    }
#line 2053 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 50:
#line 378 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds) = std::vector<std::shared_ptr<BinaryExpr>>{(yyvsp[0].sv_cond)};
    }
#line 2061 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 51:
#line 382 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_conds).push_back((yyvsp[0].sv_cond));
    }
#line 2069 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 52:
#line 390 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_group_by) = (yyvsp[0].sv_cols);
    }
#line 2077 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 53:
#line 393 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_group_by) = std::vector<std::shared_ptr<Col>>{}; }
#line 2083 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 54:
#line 399 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having) = (yyvsp[0].sv_having);
    }
#line 2091 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 55:
#line 402 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_having) = std::vector<std::shared_ptr<BinaryAggExpr>>{}; }
#line 2097 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 56:
#line 408 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having) = std::vector<std::shared_ptr<BinaryAggExpr>>{(yyvsp[0].sv_having_cond)};
    }
#line 2105 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 57:
#line 412 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having).push_back((yyvsp[0].sv_having_cond));
    }
#line 2113 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 58:
#line 421 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_agg), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2121 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 59:
#line 425 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2129 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 60:
#line 429 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_having_cond) = std::make_shared<BinaryAggExpr>((yyvsp[-2].sv_col), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_agg));
    }
#line 2137 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 61:
#line 437 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_COUNT, nullptr, "");
    }
#line 2145 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 62:
#line 441 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_COUNT, (yyvsp[-1].sv_col), "");
    }
#line 2153 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 63:
#line 445 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_SUM, (yyvsp[-1].sv_col), "");
    }
#line 2161 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 64:
#line 449 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_AVG, (yyvsp[-1].sv_col), "");
    }
#line 2169 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 65:
#line 453 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_MAX, (yyvsp[-1].sv_col), "");
    }
#line 2177 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 66:
#line 457 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_agg) = std::make_shared<AggExpr>(AGG_MIN, (yyvsp[-1].sv_col), "");
    }
#line 2185 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 67:
#line 467 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_col) = std::make_shared<Col>((yyvsp[-2].sv_str), (yyvsp[0].sv_str));
    }
#line 2193 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 68:
#line 471 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_col) = std::make_shared<Col>("", (yyvsp[0].sv_str));
    }
#line 2201 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 69:
#line 478 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cols) = std::vector<std::shared_ptr<Col>>{(yyvsp[0].sv_col)};
    }
#line 2209 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 70:
#line 482 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_cols).push_back((yyvsp[0].sv_col));
    }
#line 2217 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 71:
#line 489 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_EQ;
    }
#line 2225 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 72:
#line 493 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_LT;
    }
#line 2233 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 73:
#line 497 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_GT;
    }
#line 2241 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 74:
#line 501 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("op: +\n");
        (yyval.sv_comp_op) = SV_OP_ADD;
    }
#line 2250 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 75:
#line 506 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_SUB;
    }
#line 2258 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 76:
#line 510 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_MUL;
    }
#line 2266 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 77:
#line 514 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_DIV;
    }
#line 2274 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 78:
#line 518 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_NE;
    }
#line 2282 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 79:
#line 522 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_LE;
    }
#line 2290 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 80:
#line 526 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_comp_op) = SV_OP_GE;
    }
#line 2298 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 81:
#line 534 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("expr: value\n");
        (yyval.sv_expr) = std::static_pointer_cast<Expr>((yyvsp[0].sv_val));
    }
#line 2307 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 82:
#line 539 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("expr: col\n");
        (yyval.sv_expr) = std::static_pointer_cast<Expr>((yyvsp[0].sv_col));
    }
#line 2316 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 83:
#line 547 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_set_clauses) = std::vector<std::shared_ptr<SetClause>>{(yyvsp[0].sv_set_clause)};
    }
#line 2324 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 84:
#line 551 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_set_clauses).push_back((yyvsp[0].sv_set_clause));
    }
#line 2332 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 85:
#line 558 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("setClause\n");
        (yyval.sv_set_clause) = std::make_shared<SetClause>((yyvsp[-2].sv_str), (yyvsp[0].sv_twoexpr));
    }
#line 2341 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 86:
#line 566 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        printf("valuexpr\n");
        (yyval.sv_twoexpr) = std::make_shared<BinaryUpdateExpr>((yyvsp[-2].sv_expr), (yyvsp[-1].sv_comp_op), (yyvsp[0].sv_expr));
    }
#line 2350 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 87:
#line 571 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_twoexpr) = std::make_shared<BinaryUpdateExpr>((yyvsp[0].sv_val), SV_OP_NONE, nullptr);
    }
#line 2358 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 88:
#line 578 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items) = {};
    }
#line 2366 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 89:
#line 582 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items) = std::vector<std::shared_ptr<SelectItem>>{(yyvsp[0].sv_select_item)};
    }
#line 2374 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 90:
#line 586 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_items).push_back((yyvsp[0].sv_select_item));
    }
#line 2382 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 91:
#line 594 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_select_item) = std::make_shared<SelectItem>((yyvsp[0].sv_col), nullptr);
    }
#line 2390 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 92:
#line 598 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyvsp[-1].sv_agg)->alias = (yyvsp[0].sv_str);  // $2现在是std::string，可以直接赋值
        (yyval.sv_select_item) = std::make_shared<SelectItem>(nullptr, (yyvsp[-1].sv_agg));
    }
#line 2399 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 93:
#line 608 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = std::vector<std::pair<std::string, std::string>>{(yyvsp[0].sv_strpair)};
        (yyval.sv_table_expr).joins = {};
    }
#line 2408 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 94:
#line 613 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-2].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[0].sv_strpair));
        (yyval.sv_table_expr).joins = (yyvsp[-2].sv_table_expr).joins;
    }
#line 2418 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 95:
#line 619 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-4].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[-2].sv_strpair));
        
        // 创建JOIN表达式
        std::string left_table = (yyvsp[-4].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[-2].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, (yyvsp[0].sv_conds), INNER_JOIN);
        (yyval.sv_table_expr).joins = (yyvsp[-4].sv_table_expr).joins;
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2434 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 96:
#line 631 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-2].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[0].sv_strpair));
        (yyval.sv_table_expr).joins = (yyvsp[-2].sv_table_expr).joins;
        
        // 这种情况下JOIN条件会在WHERE子句中处理
        // 这里仅记录JOIN操作，不设置ON条件
        std::string left_table = (yyvsp[-2].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[0].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, std::vector<std::shared_ptr<BinaryExpr>>(), INNER_JOIN);
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2451 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 97:
#line 644 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_table_expr).tables = (yyvsp[-5].sv_table_expr).tables;
        (yyval.sv_table_expr).tables.push_back((yyvsp[-2].sv_strpair));
        
        // 创建SEMI JOIN表达式
        std::string left_table = (yyvsp[-5].sv_table_expr).tables.back().first;
        std::string right_table = (yyvsp[-2].sv_strpair).first;
        auto join = std::make_shared<JoinExpr>(left_table, right_table, (yyvsp[0].sv_conds), SEMI_JOIN);
        (yyval.sv_table_expr).joins = (yyvsp[-5].sv_table_expr).joins;
        (yyval.sv_table_expr).joins.push_back(join);
    }
#line 2467 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 98:
#line 660 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strpair) = std::make_pair((yyvsp[0].sv_str), "");  // 无别名时，别名为空字符串
    }
#line 2475 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 99:
#line 664 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_strpair) = std::make_pair((yyvsp[-1].sv_str), (yyvsp[0].sv_str));
    }
#line 2483 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 100:
#line 672 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_str) = (yyvsp[0].sv_str);
    }
#line 2491 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 101:
#line 676 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        (yyval.sv_str) = (yyvsp[0].sv_str);
    }
#line 2499 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 102:
#line 683 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    { 
        (yyval.sv_orderby) = (yyvsp[0].sv_orderby); 
    }
#line 2507 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 103:
#line 686 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                      { /* ignore*/ }
#line 2513 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 104:
#line 691 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    { 
        auto order = std::make_shared<OrderBy>((yyvsp[-1].sv_col), (yyvsp[0].sv_orderby_dir));
        (yyval.sv_orderby) = std::vector<std::shared_ptr<OrderBy>>{order};
    }
#line 2522 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 105:
#line 696 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
    {
        auto next_order = std::make_shared<OrderBy>((yyvsp[-1].sv_col), (yyvsp[0].sv_orderby_dir));
        (yyval.sv_orderby).push_back(next_order);
    }
#line 2531 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 106:
#line 703 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                 { (yyval.sv_orderby_dir) = OrderBy_ASC;     }
#line 2537 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 107:
#line 704 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                 { (yyval.sv_orderby_dir) = OrderBy_DESC;    }
#line 2543 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 108:
#line 705 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
            { (yyval.sv_orderby_dir) = OrderBy_DEFAULT; }
#line 2549 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 109:
#line 709 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                     { (yyval.sv_int) = (yyvsp[0].sv_int); }
#line 2555 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 110:
#line 710 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                      { (yyval.sv_int) = -1; }
#line 2561 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 111:
#line 714 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                    { (yyval.sv_setKnobType) = EnableNestLoop; }
#line 2567 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;

  case 112:
#line 715 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"
                         { (yyval.sv_setKnobType) = EnableSortMerge; }
#line 2573 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"
    break;


#line 2577 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.tab.cpp"

      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", yyr1[yyn], &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);

  *++yyvsp = yyval;
  *++yylsp = yyloc;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */
  {
    const int yylhs = yyr1[yyn] - YYNTOKENS;
    const int yyi = yypgoto[yylhs] + *yyssp;
    yystate = (0 <= yyi && yyi <= YYLAST && yycheck[yyi] == *yyssp
               ? yytable[yyi]
               : yydefgoto[yylhs]);
  }

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYEMPTY : YYTRANSLATE (yychar);

  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
#if ! YYERROR_VERBOSE
      yyerror (&yylloc, YY_("syntax error"));
#else
# define YYSYNTAX_ERROR yysyntax_error (&yymsg_alloc, &yymsg, \
                                        yyssp, yytoken)
      {
        char const *yymsgp = YY_("syntax error");
        int yysyntax_error_status;
        yysyntax_error_status = YYSYNTAX_ERROR;
        if (yysyntax_error_status == 0)
          yymsgp = yymsg;
        else if (yysyntax_error_status == 1)
          {
            if (yymsg != yymsgbuf)
              YYSTACK_FREE (yymsg);
            yymsg = YY_CAST (char *, YYSTACK_ALLOC (YY_CAST (YYSIZE_T, yymsg_alloc)));
            if (!yymsg)
              {
                yymsg = yymsgbuf;
                yymsg_alloc = sizeof yymsgbuf;
                yysyntax_error_status = 2;
              }
            else
              {
                yysyntax_error_status = YYSYNTAX_ERROR;
                yymsgp = yymsg;
              }
          }
        yyerror (&yylloc, yymsgp);
        if (yysyntax_error_status == 2)
          goto yyexhaustedlab;
      }
# undef YYSYNTAX_ERROR
#endif
    }

  yyerror_range[1] = yylloc;

  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval, &yylloc);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:
  /* Pacify compilers when the user code never invokes YYERROR and the
     label yyerrorlab therefore never appears in user code.  */
  if (0)
    YYERROR;

  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYTERROR;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYTERROR)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;

      yyerror_range[1] = *yylsp;
      yydestruct ("Error: popping",
                  yystos[yystate], yyvsp, yylsp);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  yyerror_range[2] = yylloc;
  /* Using YYLLOC is tempting, but would change the location of
     the lookahead.  YYLOC is available though.  */
  YYLLOC_DEFAULT (yyloc, yyerror_range, 2);
  *++yylsp = yyloc;

  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", yystos[yyn], yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturn;


/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturn;


#if !defined yyoverflow || YYERROR_VERBOSE
/*-------------------------------------------------.
| yyexhaustedlab -- memory exhaustion comes here.  |
`-------------------------------------------------*/
yyexhaustedlab:
  yyerror (&yylloc, YY_("memory exhausted"));
  yyresult = 2;
  /* Fall through.  */
#endif


/*-----------------------------------------------------.
| yyreturn -- parsing is finished, return the result.  |
`-----------------------------------------------------*/
yyreturn:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval, &yylloc);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  yystos[+*yyssp], yyvsp, yylsp);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif
#if YYERROR_VERBOSE
  if (yymsg != yymsgbuf)
    YYSTACK_FREE (yymsg);
#endif
  return yyresult;
}
#line 722 "/home/<USER>/db2025/db2025-x1/src/parser/yacc.y"

