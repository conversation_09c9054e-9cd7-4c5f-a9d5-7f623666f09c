/* A Bison parser, made by GNU Bison 3.5.1.  */

/* Bison interface for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2020 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* Undocumented macros, especially those whose name start with YY_,
   are private implementation details.  Do not rely on them.  */

#ifndef YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
# define YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 0
#endif
#if YYDEBUG
extern int yydebug;
#endif

/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    SHOW = 258,
    TABLES = 259,
    CREATE = 260,
    TABLE = 261,
    DROP = 262,
    DESC = 263,
    INSERT = 264,
    INTO = 265,
    VALUES = 266,
    DELETE = 267,
    FROM = 268,
    ASC = 269,
    EXPLAIN = 270,
    ORDER = 271,
    BY = 272,
    ON = 273,
    SEMI = 274,
    COUNT = 275,
    MIN = 276,
    MAX = 277,
    AVG = 278,
    SUM = 279,
    GROUP = 280,
    HAVING = 281,
    AS = 282,
    LOAD = 283,
    WHERE = 284,
    UPDATE = 285,
    SET = 286,
    SELECT = 287,
    INT = 288,
    CHAR = 289,
    FLOAT = 290,
    INDEX = 291,
    AND = 292,
    JOIN = 293,
    EXIT = 294,
    HELP = 295,
    TXN_BEGIN = 296,
    TXN_COMMIT = 297,
    TXN_ABORT = 298,
    TXN_ROLLBACK = 299,
    ORDER_BY = 300,
    ENABLE_NESTLOOP = 301,
    ENABLE_SORTMERGE = 302,
    STATIC_CHECKPOINT = 303,
    LIMIT = 304,
    LEQ = 305,
    NEQ = 306,
    GEQ = 307,
    T_EOF = 308,
    IDENTIFIER = 309,
    VALUE_STRING = 310,
    PATH_TOKEN = 311,
    VALUE_INT = 312,
    VALUE_FLOAT = 313,
    VALUE_BOOL = 314
  };
#endif

/* Value type.  */

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int yyparse (void);

#endif /* !YY_YY_HOME_ZSCORE_DB2025_DB2025_X1_SRC_PARSER_YACC_TAB_H_INCLUDED  */
