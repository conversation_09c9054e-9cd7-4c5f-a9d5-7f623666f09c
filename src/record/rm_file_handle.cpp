/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "rm_file_handle.h"

/**
 * @description: 获取当前表中记录号为rid的记录
 * @param {Rid&} rid 记录号，指定记录的位置
 * @param {Context*} context
 * @return {unique_ptr<RmRecord>} rid对应的记录对象指针
 */
std::unique_ptr<RmRecord> RmFileHandle::get_record(const Rid& rid, Context* context) const {
    // Todo:
    // 1. 获取指定记录所在的page handle
    // 2. 初始化一个指向RmRecord的指针（赋值其内部的data和size）
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    char *record_data = page_handle.get_slot(rid.slot_no);
    auto record = std::make_unique<RmRecord>(file_hdr_.record_size,record_data);
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),false);
    return record;
}

/**
 * @description: 在当前表中插入一条记录，不指定插入位置
 * @param {char*} buf 要插入的记录的数据
 * @param {Context*} context
 * @return {Rid} 插入的记录的记录号（位置）
 */
Rid RmFileHandle::insert_record(char* buf, Context* context) {
    // Todo:
    // 1. 获取当前未满的page handle
    // 2. 在page handle中找到空闲slot位置
    // 3. 将buf复制到空闲slot位置
    // 4. 更新page_handle.page_hdr中的数据结构
    // 注意考虑插入一条记录后页面已满的情况，需要更新file_hdr_.first_free_page_no
    // int free_page_id = file_hdr_.first_free_page_no;
    // PageId pid{fd_,(page_id_t)free_page_id};
    // buffer_pool_manager_->fetch_page(pid);
    
    /*1.获取当前未满的页的page handle*/
    RmPageHandle page_handle = create_page_handle();

    /*2.遍历获取第一个空槽*/
    int first_free_slot_no = Bitmap::first_bit(false, page_handle.bitmap, file_hdr_.num_records_per_page);
    if(first_free_slot_no < file_hdr_.num_records_per_page)
    {
        char *TuplePtr = page_handle.get_slot(first_free_slot_no);
        memcpy(TuplePtr,buf,file_hdr_.record_size);
        Bitmap::set(page_handle.bitmap, first_free_slot_no);
    }
    else
    {
        printf("INvalid slot_no\n");
    }


    /*3.更新page_hdr*/
    page_handle.page_hdr->num_records++;

    /*4.处理插入后当前页满的情况,slot是0索引*/
    if(Bitmap::next_bit(false,page_handle.bitmap,page_handle.file_hdr->num_records_per_page,first_free_slot_no) == page_handle.file_hdr->num_records_per_page)
    {
        if(page_handle.page_hdr->next_free_page_no != INVALID_PAGE_ID)
        {
            file_hdr_.first_free_page_no = page_handle.page_hdr->next_free_page_no;
        }
        else
        {
            // buffer_pool_manager_->new_page(&new_page_id);
            // page_handle.page_hdr->next_free_page_no = new_page_id.page_no;
            file_hdr_.first_free_page_no = INVALID_PAGE_ID;
            // buffer_pool_manager_->unpin_page(new_page_id,true);
            buffer_pool_manager_->unpin_page(create_new_page_handle().page->get_page_id(),true);
        }
    }
    int page_no = page_handle.page->get_page_id().page_no;
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),true);
    return Rid{page_no,first_free_slot_no};
}

/**
 * @description: 在当前表中的指定位置插入一条记录
 * @param {Rid&} rid 要插入记录的位置
 * @param {char*} buf 要插入记录的数据
 */
void RmFileHandle::insert_record(const Rid& rid, char* buf) {
    /*1.获取该页的page handle*/
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    /*2.定位到目标槽地址*/
    char *TuplePtr = page_handle.get_slot(rid.slot_no);
    memcpy(TuplePtr,buf,file_hdr_.record_size);
    Bitmap::set(page_handle.bitmap, rid.slot_no);

    /*3.解pin*/
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),true);
}

/**
 * @description: 删除记录文件中记录号为rid的记录
 * @param {Rid&} rid 要删除的记录的记录号（位置）
 * @param {Context*} context
 */
void RmFileHandle::delete_record(const Rid& rid, Context* context) {
    // Todo:
    // 1. 获取指定记录所在的page handle
    // 2. 更新page_handle.page_hdr中的数据结构
    // 注意考虑删除一条记录后页面未满的情况，需要调用release_page_handle()

    /*1.获取该页的page handle*/
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    /*2.定位到目标槽地址*/
    char *TuplePtr = page_handle.get_slot(rid.slot_no);
    memset(TuplePtr,0,file_hdr_.record_size);
    Bitmap::reset(page_handle.bitmap, rid.slot_no);

    /*3.该页从满页变成未满页*/
    if(file_hdr_.first_free_page_no != rid.page_no)
    {
        release_page_handle(page_handle);
    }

    /*4.解pin*/
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),true);
}


/**
 * @description: 更新记录文件中记录号为rid的记录
 * @param {Rid&} rid 要更新的记录的记录号（位置）
 * @param {char*} buf 新记录的数据
 * @param {Context*} context
 */
void RmFileHandle::update_record(const Rid& rid, char* buf, Context* context) {
    // Todo:
    // 1. 获取指定记录所在的page handle
    // 2. 更新记录

    /*1.获取该页的page handle*/
    RmPageHandle page_handle = fetch_page_handle(rid.page_no);
    
    /*2.定位到目标槽地址*/
    char *TuplePtr = page_handle.get_slot(rid.slot_no);
    memcpy(TuplePtr,buf,file_hdr_.record_size);

    /*4.解pin*/
    buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),true);
}

/**
 * 以下函数为辅助函数，仅提供参考，可以选择完成如下函数，也可以删除如下函数，在单元测试中不涉及如下函数接口的直接调用
*/
/**
 * @description: 获取指定页面的页面句柄
 * @param {int} page_no 页面号
 * @return {RmPageHandle} 指定页面的句柄
 */
RmPageHandle RmFileHandle::fetch_page_handle(int page_no) const {
    // Todo:
    // 使用缓冲池获取指定页面，并生成page_handle返回给上层
    // if page_no is invalid, throw PageNotExistError exception
    if(page_no == INVALID_PAGE_ID)
    {
        throw PageNotExistError(disk_manager_->get_file_name(fd_),page_no);
    }
    return RmPageHandle(&file_hdr_, buffer_pool_manager_->fetch_page({fd_,page_no}));
}

/**
 * @description: 创建一个新的page handle
 * @return {RmPageHandle} 新的PageHandle
 */
RmPageHandle RmFileHandle::create_new_page_handle() {
    // Todo:
    // 1.使用缓冲池来创建一个新page
    // 2.更新page handle中的相关信息
    // 3.更新file_hdr_
    
    /*1.先获取一个新页*/
    PageId *new_page_id = new PageId();
    new_page_id->fd = fd_;

    Page *new_page = buffer_pool_manager_->new_page(new_page_id);
    RmPageHandle new_page_handle{&file_hdr_, new_page};
    
    /*2.更新file_hdr_*/
    /*2.1更新file_hdr_中的最后一个空闲页的下一个空闲页*/
    if(new_page)
    {
        if(file_hdr_.first_free_page_no != INVALID_PAGE_ID)
        {
            RmPageHandle page_handle = fetch_page_handle(file_hdr_.first_free_page_no);
            while(page_handle.page_hdr->next_free_page_no != INVALID_PAGE_ID)
            {
                buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),false);
                page_handle = fetch_page_handle(page_handle.page_hdr->next_free_page_no);
            }
            page_handle.page_hdr->next_free_page_no = new_page_id->page_no;
            buffer_pool_manager_->unpin_page(page_handle.page->get_page_id(),true);
        }
        else
        {
            new_page_handle.page_hdr->next_free_page_no = INVALID_PAGE_ID;
        }
        /*2.2更新file_hdr_中的的页数*/
        file_hdr_.num_pages++;
        file_hdr_.first_free_page_no = new_page_id->page_no;
    }

    return new_page_handle;
}

/**
 * @brief 创建或获取一个空闲的page handle
 *
 * @return RmPageHandle 返回生成的空闲page handle
 * @note pin the page, remember to unpin it outside!
 */
RmPageHandle RmFileHandle::create_page_handle() {
    // Todo:
    // 1. 判断file_hdr_中是否还有空闲页
    //     1.1 没有空闲页：使用缓冲池来创建一个新page；可直接调用create_new_page_handle()
    //     1.2 有空闲页：直接获取第一个空闲页
    // 2. 生成page handle并返回给上层

    /*1.判断file_hdr_中是否还有空闲页*/
    /*1.1file_hdr_中有空闲页*/
    if(file_hdr_.first_free_page_no != INVALID_PAGE_ID)
    {
        return fetch_page_handle(file_hdr_.first_free_page_no);
    }
    /*1.2file_hdr_中没有空闲页*/
    else
    {
        return create_new_page_handle();
    }
    return RmPageHandle(&file_hdr_, nullptr);
}

/**
 * @description: 当一个页面从没有空闲空间的状态变为有空闲空间状态时，更新文件头和页头中空闲页面相关的元数据
 */
void RmFileHandle::release_page_handle(RmPageHandle&page_handle) {
    // Todo:
    // 当page从已满变成未满，考虑如何更新：
    // 1. page_handle.page_hdr->next_free_page_no
    // 2. file_hdr_.first_free_page_no
    
    /*1.记录第一个非空页*/
    int old_first_free_page_no = file_hdr_.first_free_page_no;
    
    /*2.若该页在第一个空闲页的前面则该页变成第一个非空页*/
    if(page_handle.page->get_page_id().page_no < old_first_free_page_no)
    {
        file_hdr_.first_free_page_no = page_handle.page->get_page_id().page_no;
        /*2.1更新该页的下一个非空页*/
        page_handle.page_hdr->next_free_page_no = old_first_free_page_no;
    }
    /*3.若在后面，则插入空闲链表*/
    else
    {
        auto new_empty_page = page_handle.page->get_page_id().page_no;
        auto curr_free_page_page_handle = fetch_page_handle(file_hdr_.first_free_page_no);
        auto curr_free_page_next_page_no = curr_free_page_page_handle.page_hdr->next_free_page_no;
        while(curr_free_page_next_page_no != INVALID_PAGE_ID)
        {
            // printf("pageno %d\n",curr_free_page_page_handle.page->get_page_id().page_no);
            // printf("curr_free_page_next_page_no %d\n",curr_free_page_next_page_no);
            /*3.1 若是下一个空闲页的页号比现在的页号小，则进入下一个空闲页*/
            if(curr_free_page_next_page_no < new_empty_page)
            {
                buffer_pool_manager_->unpin_page(curr_free_page_page_handle.page->get_page_id(),false);
                curr_free_page_page_handle = fetch_page_handle(curr_free_page_next_page_no);
                curr_free_page_next_page_no = curr_free_page_page_handle.page_hdr->next_free_page_no;
                continue;
            }
            /*3.2 若是等于则直接返回*/
            else if(curr_free_page_next_page_no == new_empty_page)
            {
                buffer_pool_manager_->unpin_page(curr_free_page_page_handle.page->get_page_id(),false);
                return;
            }
            /*3.3 若是大于则跳出*/
            else
            {
                break;
            }
        }
        /*3.4 放入当前位置*/
        curr_free_page_page_handle.page_hdr->next_free_page_no = new_empty_page;
        page_handle.page_hdr->next_free_page_no = curr_free_page_next_page_no;
        buffer_pool_manager_->unpin_page(curr_free_page_page_handle.page->get_page_id(),true);
    }
}

int RmFileHandle::get_num_records()
{
    int num_records = 0;
    for(int i = 1;i < file_hdr_.num_pages; i++)
    {
        auto page_handle = fetch_page_handle(i);
        num_records += page_handle.page_hdr->num_records;
    }
    return num_records;
}