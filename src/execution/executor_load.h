/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "execution_common.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include <fstream>
#include <sstream>
#include <vector>

class LoadExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                    // 表的元数据
    RmFileHandle *fh_;               // 表的数据文件句柄
    std::string tab_name_;           // 表名称
    std::string file_name_;          // 文件名称
    SmManager *sm_manager_;
    std::ifstream csv_file_;         // CSV文件流
    std::vector<std::vector<Value>> all_records_; // 存储所有解析后的记录
    size_t current_idx_;             // 当前处理的记录索引
    bool is_loaded_;                 // 是否已加载所有数据
    Rid rid_;                        // 最后插入记录的Rid

   public:
    LoadExecutor(SmManager *sm_manager, const std::string &tab_name, const std::string &file_name, Context *context) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        tab_name_ = tab_name;
        file_name_ = file_name;
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
        current_idx_ = 0;
        is_loaded_ = false;

        csv_file_.open(file_name_);
        if (!csv_file_.is_open()) {
            throw FileNotFoundError(file_name_);
        }

        load_all_records();
    }
    
    ~LoadExecutor() override {
        if (csv_file_.is_open()) {
            csv_file_.close();
        }
    }

    // 一次性加载所有CSV记录到内存
    void load_all_records() {
        std::string line;
        while (std::getline(csv_file_, line)) {
            try {
                std::vector<Value> values = parse_csv_line(line);
                all_records_.push_back(values);
            } catch (const std::exception& e) {
                std::cerr << "Error parsing line: " << line << ", error: " << e.what() << std::endl;
                continue; // 跳过错误行
            }
        }
        is_loaded_ = true;
    }

    // 解析CSV行数据为Value向量
    std::vector<Value> parse_csv_line(const std::string& line) {
        std::vector<Value> values;
        std::stringstream ss(line);
        std::string field;
        size_t col_idx = 0;
        
        while (std::getline(ss, field, ',') && col_idx < tab_.cols.size()) {
            const ColMeta& col = tab_.cols[col_idx];
            Value val;
            
            // 根据列类型解析字段值
            switch (col.type) {
                case TYPE_INT:
                    val.set_int(std::stoi(field));
                    val.type = TYPE_INT;
                    break;
                case TYPE_FLOAT:
                    val.set_float(std::stof(field));
                    val.type = TYPE_FLOAT;
                    break;
                case TYPE_STRING:
                    // 处理带引号的字符串
                    if (!field.empty() && field.front() == '"' && field.back() == '"') {
                        field = field.substr(1, field.length() - 2);
                    }
                    val.set_str(field);
                    val.type = TYPE_STRING;
                    break;
                default:
                    throw IncompatibleTypeError("Unsupported type", coltype2str(col.type));
            }
            
            values.push_back(val);
            col_idx++;
        }
        
        // 检查字段数量是否与表列数一致
        if (values.size() < tab_.cols.size()) {
            throw InternalError("CSV line has fewer fields than table columns");
        }
        
        return values;
    }

    // 实现Next接口，首次调用时一次性插入所有记录
    std::unique_ptr<RmRecord> Next() override {
        // 如果所有记录已处理完毕，直接返回nullptr
        if (current_idx_ >= all_records_.size()) {
            return nullptr;
        }
        
        try {
            // 一次性插入所有记录
            for (auto& values : all_records_) {
                // 创建记录缓冲区
                RmRecord rec(fh_->get_file_hdr().record_size);
                for (size_t i = 0; i < values.size(); i++) {
                    auto& col = tab_.cols[i];
                    auto& val = values[i];
                    
                    // 处理类型转换
                    if (col.type != val.type) {
                        if (col.type == TYPE_FLOAT && val.type == TYPE_INT) {
                            val.type = TYPE_FLOAT;
                            val.set_float(static_cast<float>(val.int_val));
                        } else {
                            throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
                        }
                    }
                    
                    val.init_raw(col.len);
                    memcpy(rec.data + col.offset, val.raw->data, col.len);
                }

                // 插入记录到数据文件
                rid_ = fh_->insert_record(rec.data, context_);
                
                // 更新相关索引
                for (size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
                    auto& index = tab_.indexes[idx];
                    auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
                    char* key = new char[index.col_tot_len];
                    int offset = 0;
                    
                    for (size_t i = 0; i < index.col_num; ++i) {
                        memcpy(key + offset, rec.data + index.cols[i].offset, index.cols[i].len);
                        offset += index.cols[i].len;
                    }
                    
                    ih->insert_entry(key, rid_, context_->txn_);
                    delete[] key; // 释放索引键内存
                }
            }
            
            // 标记所有记录已处理
            current_idx_ = all_records_.size();
        } catch (const std::exception& e) {
            throw; // 向上层抛出异常
        }
        
        return nullptr;
    }

    // 返回最后插入记录的Rid
    Rid &rid() override {
        return rid_;
    }
};