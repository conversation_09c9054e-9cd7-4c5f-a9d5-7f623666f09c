/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "execution_common.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include <fstream>
#include <sstream>
#include <vector>

class LoadExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                    // 表的元数据
    RmFileHandle *fh_;               // 表的数据文件句柄
    std::string tab_name_;           // 表名称
    std::string file_name_;          // 文件名称
    SmManager *sm_manager_;
    std::ifstream csv_file_;         // CSV文件流
    std::vector<std::string> raw_lines_; // 存储原始CSV行数据
    size_t current_idx_;             // 当前处理的记录索引
    bool is_loaded_;                 // 是否已加载所有数据
    Rid rid_;                        // 最后插入记录的Rid

    // 预分配的缓冲区，避免重复分配
    std::unique_ptr<char[]> record_buffer_;
    std::vector<std::unique_ptr<char[]>> index_key_buffers_;

    // 批量处理参数
    static constexpr size_t BATCH_SIZE = 1000; // 每批处理的记录数

    // 缓存索引句柄，避免重复查找
    std::vector<IxIndexHandle*> index_handles_;

   public:
    LoadExecutor(SmManager *sm_manager, const std::string &tab_name, const std::string &file_name, Context *context) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        tab_name_ = tab_name;
        file_name_ = file_name;
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
        current_idx_ = 0;
        is_loaded_ = false;

        csv_file_.open(file_name_);
        if (!csv_file_.is_open()) {
            throw FileNotFoundError(file_name_);
        }

        // 预分配记录缓冲区
        record_buffer_ = std::make_unique<char[]>(fh_->get_file_hdr().record_size);

        // 预分配索引键缓冲区并缓存索引句柄
        index_key_buffers_.reserve(tab_.indexes.size());
        index_handles_.reserve(tab_.indexes.size());
        for (const auto& index : tab_.indexes) {
            index_key_buffers_.push_back(std::make_unique<char[]>(index.col_tot_len));
            auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            index_handles_.push_back(ih);
        }

        load_all_records();
    }
    
    ~LoadExecutor() override {
        if (csv_file_.is_open()) {
            csv_file_.close();
        }
    }

    // 一次性加载所有CSV行到内存（延迟解析）
    void load_all_records() {
        std::string line;
        // 跳过表头
        if (std::getline(csv_file_, line)) {
            // 第一行是表头，跳过
        }

        // 预分配空间以减少vector重新分配
        raw_lines_.reserve(10000); // 预估记录数

        // 加载所有数据行
        while (std::getline(csv_file_, line)) {
            if (!line.empty()) {
                raw_lines_.push_back(std::move(line));
            }
        }

        // 释放多余的内存
        raw_lines_.shrink_to_fit();
        is_loaded_ = true;
    }

    // 高效解析CSV行并直接填充到记录缓冲区
    void parse_csv_line_to_record(const std::string& line, char* record_data) {
        const char* ptr = line.c_str();
        const char* end = ptr + line.length();
        size_t col_idx = 0;

        while (ptr < end && col_idx < tab_.cols.size()) {
            const ColMeta& col = tab_.cols[col_idx];
            const char* field_start = ptr;

            // 找到字段结束位置
            while (ptr < end && *ptr != ',') {
                ptr++;
            }

            size_t field_len = ptr - field_start;

            // 根据列类型直接解析并写入记录缓冲区
            switch (col.type) {
                case TYPE_INT: {
                    int val = 0;
                    // 手动解析整数，避免创建临时字符串
                    bool negative = false;
                    const char* num_ptr = field_start;
                    if (*num_ptr == '-') {
                        negative = true;
                        num_ptr++;
                    }
                    while (num_ptr < field_start + field_len) {
                        if (*num_ptr >= '0' && *num_ptr <= '9') {
                            val = val * 10 + (*num_ptr - '0');
                        }
                        num_ptr++;
                    }
                    if (negative) val = -val;
                    *(int*)(record_data + col.offset) = val;
                    break;
                }
                case TYPE_FLOAT: {
                    // 创建临时字符串用于float解析（相对较少的float字段）
                    std::string temp_str(field_start, field_len);
                    float val = std::stof(temp_str);
                    *(float*)(record_data + col.offset) = val;
                    break;
                }
                case TYPE_STRING: {
                    // 处理字符串字段
                    const char* str_start = field_start;
                    size_t str_len = field_len;

                    // 处理带引号的字符串
                    if (str_len >= 2 && *str_start == '"' && *(field_start + field_len - 1) == '"') {
                        str_start++;
                        str_len -= 2;
                    }

                    // 直接拷贝到记录缓冲区，确保不超过列长度
                    memset(record_data + col.offset, 0, col.len);
                    size_t copy_len = std::min(str_len, (size_t)col.len - 1);
                    memcpy(record_data + col.offset, str_start, copy_len);
                    break;
                }
                default:
                    throw IncompatibleTypeError("Unsupported type", coltype2str(col.type));
            }

            col_idx++;
            if (ptr < end) ptr++; // 跳过逗号
        }

        // 检查字段数量是否与表列数一致
        if (col_idx < tab_.cols.size()) {
            throw InternalError("CSV line has fewer fields than table columns");
        }
    }

    // 实现Next接口，分批插入记录以优化性能
    std::unique_ptr<RmRecord> Next() override {
        // 如果所有记录已处理完毕，直接返回nullptr
        if (current_idx_ >= raw_lines_.size()) {
            return nullptr;
        }

        try {
            // 分批处理记录，减少内存压力和提高缓存效率
            size_t batch_end = std::min(current_idx_ + BATCH_SIZE, raw_lines_.size());

            for (size_t i = current_idx_; i < batch_end; ++i) {
                const auto& line = raw_lines_[i];

                // 直接解析CSV行到预分配的记录缓冲区
                parse_csv_line_to_record(line, record_buffer_.get());

                // 插入记录到数据文件
                rid_ = fh_->insert_record(record_buffer_.get(), context_);

                // 批量更新相关索引，使用预分配的索引键缓冲区
                update_indexes_for_record(rid_);
            }

            // 更新当前处理索引
            current_idx_ = batch_end;

            // 如果还有未处理的记录，返回一个虚拟记录表示继续处理
            if (current_idx_ < raw_lines_.size()) {
                return std::make_unique<RmRecord>(0);
            }

        } catch (const std::exception& e) {
            throw; // 向上层抛出异常
        }

        return nullptr;
    }

    // 为单条记录更新所有索引（优化版本）
    void update_indexes_for_record(const Rid& rid) {
        for (size_t idx = 0; idx < tab_.indexes.size(); ++idx) {
            const auto& index = tab_.indexes[idx];
            auto ih = index_handles_[idx]; // 使用缓存的索引句柄
            char* key = index_key_buffers_[idx].get();
            int offset = 0;

            // 构建索引键 - 优化内存拷贝
            for (size_t i = 0; i < index.col_num; ++i) {
                const auto& col = index.cols[i];
                memcpy(key + offset, record_buffer_.get() + col.offset, col.len);
                offset += col.len;
            }

            // 插入索引项
            ih->insert_entry(key, rid, context_->txn_);
        }
    }

    // 返回最后插入记录的Rid
    Rid &rid() override {
        return rid_;
    }
};
