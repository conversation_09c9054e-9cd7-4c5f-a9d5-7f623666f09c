/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class UpdateExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;
    std::vector<Condition> conds_;
    RmFileHandle *fh_;
    std::vector<Rid> rids_;
    std::string tab_name_;
    std::vector<SetClause> set_clauses_;
    SmManager *sm_manager_;

   public:
    UpdateExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<SetClause> set_clauses,
                   std::vector<Condition> conds, std::vector<Rid> rids, Context *context) {
        sm_manager_ = sm_manager;
        tab_name_ = tab_name;
        set_clauses_ = set_clauses;
        tab_ = sm_manager_->db_.get_table(tab_name);
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        conds_ = conds;
        rids_ = rids;
        context_ = context;
    }
    std::unique_ptr<RmRecord> Next() override {

    // 1. 提取本次更新涉及的列名
    std::unordered_set<std::string> updated_cols;
    for (auto &clause : set_clauses_) {
        updated_cols.insert(clause.lhs.col_name);
    }

    // 2. 遍历所有要更新的元组
    for (const auto &rid : rids_) {
        RmRecord record(fh_->get_file_hdr().record_size);
        std::unique_ptr<RmRecord> targetRecord = fh_->get_record(rid,context_);
        record.SetData(targetRecord->data);
        // 3. 更新新 record 中的字段值
        for(const auto& rid : rids_)
        {
            /*2.1构造新的元组*/
            for(const auto& clause : set_clauses_)
            {
                TabCol lhs = clause.lhs;
                SetValue rvalue = clause.rhs;
                Value rhs;
                auto col = tab_.get_col(lhs.col_name);
                if(rvalue.is_expr)
                {
                    /*rcol是表达式中的那个列，rhs是表达式的值，
                    col2val是从元组中取到的rcol的值,final_value是expr1 + expr2的值*/
                    TabCol rcol;
                    Value col2val;
                    Value final_value;
                    bool first_isvalue = false;
                    if(rvalue.first_is_col && rvalue.second_is_val)
                    {
                        rcol = rvalue.first_col;
                        rhs = rvalue.second_val;
                    }
                    else if(rvalue.second_is_col && rvalue.first_is_val)
                    {
                        rcol = rvalue.second_col;
                        rhs = rvalue.first_val;
                        first_isvalue = true;
                    }

                    /*从元组中取值到col2val */
                    auto rcolmeta = tab_.get_col(rcol.col_name);
                    std::string str(targetRecord->data + rcolmeta->offset,rcolmeta->len);
                    switch(col->type)
                    {
                        case TYPE_INT:
                            col2val.set_int(*reinterpret_cast<const int*>(targetRecord->data + rcolmeta->offset));
                            break;
                        case TYPE_FLOAT:
                            col2val.set_float(*reinterpret_cast<const float*>(targetRecord->data + rcolmeta->offset));
                            break;
                        case TYPE_STRING:
                            col2val.set_str(str);
                            break;
                        default:
                            throw InternalError("invalid type\n");
                    }
                    col2val.init_raw(col->len);

                    /*第一个expr的类型检查，要么同类型要么int->float*/
                    if (col->type != col2val.type) {
                        if((col->type == TYPE_STRING && col2val.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && col2val.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && col2val.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(col2val.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            col2val.type = TYPE_FLOAT;
                            col2val.set_float(float(rhs.int_val));
                        }
                    }

                    /*第二个expr的类型检查，要么同类型要么int->float*/
                    if (col->type != rhs.type) {
                        if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            rhs.type = TYPE_FLOAT;
                            rhs.set_float(float(rhs.int_val));
                        }
                    }

                    /*现在两个expr的类型相同且都等于左列，可以直接运算*/
                    switch(col->type)
                    {
                        case TYPE_INT:
                        {
                            if(rvalue.op == OP_ADD)
                            {
                                final_value.set_int(col2val.int_val + rhs.int_val);
                            }
                            else if(rvalue.op == OP_SUB)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_int(rhs.int_val - col2val.int_val);
                                }
                                else
                                {
                                    final_value.set_int(col2val.int_val - rhs.int_val);
                                }
                            }
                            else if(rvalue.op == OP_MUL)
                            {
                                final_value.set_int(col2val.int_val * rhs.int_val);
                            }
                            else if(rvalue.op == OP_DIV)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_int(rhs.int_val / col2val.int_val);
                                }
                                else
                                {
                                    final_value.set_int(col2val.int_val / rhs.int_val);
                                }
                            }
                            else
                            {
                                throw InternalError("invalid type\n");
                            }
                            break;
                        }
                        case TYPE_FLOAT:
                        {
                            if(rvalue.op == OP_ADD)
                            {
                                final_value.set_float(col2val.float_val + rhs.float_val);
                            }
                            else if(rvalue.op == OP_SUB)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_float(rhs.float_val - col2val.float_val);
                                }
                                else
                                {
                                    final_value.set_float(col2val.float_val - rhs.float_val);
                                }
                            }
                            else if(rvalue.op == OP_MUL)
                            {
                                final_value.set_float(col2val.float_val * rhs.float_val);
                            }
                            else if(rvalue.op == OP_DIV)
                            {
                                if(first_isvalue)
                                {
                                    final_value.set_float(rhs.float_val / col2val.float_val);
                                }
                                else
                                {
                                    final_value.set_float(col2val.float_val / rhs.float_val);
                                }
                            }
                            else
                            {
                                throw InternalError("invalid type\n");
                            }
                            break;
                        }
                        case TYPE_STRING:
                        {
                            if(rvalue.op != OP_ADD)
                            {
                                throw InternalError("invalid type\n");
                            }
                            final_value.set_str(col2val.str_val + rhs.str_val);
                            break;
                        }
                        default:
                            throw InternalError("invalid type\n");
                    }
                    final_value.init_raw(col->len);
                    memcpy(record.data + col->offset, final_value.raw->data,col->len);
                }
                else
                {
                    rhs = rvalue.first_val;
                    if (col->type != rhs.type) {
                        if((col->type == TYPE_STRING && rhs.type != TYPE_STRING) 
                        || (col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        || (col->type != TYPE_STRING && rhs.type == TYPE_STRING))
                        {
                            throw IncompatibleTypeError(coltype2str(col->type), coltype2str(rhs.type));
                        }
                        // else if(col->type == TYPE_INT && rhs.type == TYPE_FLOAT)
                        // {
                        //     rhs.type = TYPE_INT;
                        //     rhs.set_int(int(rhs.float_val));
                        // }
                        else
                        {
                            rhs.type = TYPE_FLOAT;
                            rhs.set_float(float(rhs.int_val));
                        }
                    }
                    rhs.init_raw(col->len);
                    memcpy(record.data + col->offset, rhs.raw->data,col->len);
                }
            }
        }

        // 4. 构造新 key 并检查唯一性（仅检查受影响的索引）
        std::vector<std::unique_ptr<char[]>> new_keys;  // 缓存 key，后续重复用
        std::vector<IxIndexHandle *> related_indexes;   // 对应 ih
        for (auto &index : tab_.indexes) {
            // 检查这个索引是否和更新字段有关
            bool related = false;
            for (auto &col : index.cols) {
                if (updated_cols.count(col.name)) {
                    related = true;
                    break;
                }
            }
            if (!related) continue;

            // 构造新 key
            auto key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            for (int j = 0; j < index.col_num; ++j) {
                memcpy(key.get() + offset, record.data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }

            // 获取对应 ih 并检查唯一性（insert_entry 会抛异常）
            auto ih = sm_manager_->ihs_.at(
                sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
            ih->insert_entry(key.get(), rid, context_->txn_);  // 预插入验证唯一性

            related_indexes.push_back(ih);
            new_keys.push_back(std::move(key));
        }

        // 5. 删除旧索引项（仅删除受影响的索引）
        for (size_t i = 0; i < related_indexes.size(); ++i) {
            auto ih = related_indexes[i];
            auto &index = tab_.indexes[i];  // 找到对应 index meta
            std::unique_ptr<char[]> old_key = std::make_unique<char[]>(index.col_tot_len);
            int offset = 0;
            for (int j = 0; j < index.col_num; ++j) {
                memcpy(old_key.get() + offset, targetRecord->data + index.cols[j].offset, index.cols[j].len);
                offset += index.cols[j].len;
            }
            ih->delete_entry(old_key.get(), context_->txn_);
        }

        // 6. 更新表记录
        fh_->update_record(rid, record.data, context_);

        /*3.写事务的写集合*/
        WriteRecord *wr = new WriteRecord(WType::UPDATE_TUPLE, tab_name_, rid, *(targetRecord.get()));
        context_->txn_->append_write_record(wr);
    }

    return nullptr;
}

    Rid &rid() override { return _abstract_rid; }
};
