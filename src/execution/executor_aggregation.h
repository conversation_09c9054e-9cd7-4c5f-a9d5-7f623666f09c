/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "parser/ast.h"
#include "common/common.h"
#include <unordered_map>
#include <map>
#include <map>

// 聚合结果存储结构
struct AggregateResult {
    std::map<std::string, Value> values;  // 存储聚合函数的结果，key为别名或函数名
    
    AggregateResult() = default;
};

// 分组键结构，用于GROUP BY
struct GroupKey {
    std::vector<Value> key_values;
    
    GroupKey() = default;
    
    GroupKey(const std::vector<Value>& values) : key_values(values) {}
    
    bool operator<(const GroupKey& other) const {
        if (key_values.size() != other.key_values.size()) {
            return key_values.size() < other.key_values.size();
        }
        
        for (size_t i = 0; i < key_values.size(); ++i) {
            const Value& v1 = key_values[i];
            const Value& v2 = other.key_values[i];
            
            if (v1.type != v2.type) {
                return v1.type < v2.type;
            }
            
            switch (v1.type) {
                case TYPE_INT:
                    if (v1.int_val != v2.int_val) {
                        return v1.int_val < v2.int_val;
                    }
                    break;
                case TYPE_FLOAT:
                    if (std::abs(v1.float_val - v2.float_val) > 1e-6f) {
                        return v1.float_val < v2.float_val;
                    }
                    break;
                case TYPE_STRING:
                    if (v1.str_val != v2.str_val) {
                        return v1.str_val < v2.str_val;
                    }
                    break;
                default:
                    break;
            }
        }
        return false;  // 所有值都相等
    }
};

// 单个聚合函数的状态
struct SingleAggState {
    int count = 0;
    double sum = 0.0;
    Value min_val;
    Value max_val;
    bool has_value = false;

    SingleAggState() = default;
};

// 聚合状态，用于累积计算，为每个聚合函数维护独立状态
struct AggregateState {
    std::map<std::string, SingleAggState> agg_states;  // key是聚合函数的标识符

    AggregateState() = default;
};

class AggregationExecutor : public AbstractExecutor {
private:
    std::unique_ptr<AbstractExecutor> prev_;           // 子执行器
    std::vector<TabCol> group_cols_;                   // GROUP BY 列
    std::vector<ast::AggExpr> agg_exprs_;             // 聚合表达式
    std::vector<Condition> having_conds_;             // HAVING 条件
    std::vector<TabCol> sel_cols_;                    // 选择列
    
    // 执行状态
    std::map<GroupKey, AggregateState> group_states_; // 分组聚合状态
    std::map<GroupKey, AggregateResult> results_;     // 最终结果
    std::map<GroupKey, AggregateResult>::iterator current_result_; // 当前结果迭代器
    bool is_executed_ = false;                        // 是否已执行聚合
    
    // 列信息
    std::vector<ColMeta> output_cols_;                // 输出列元数据
    size_t tuple_len_;                                // 输出元组长度

public:
    AggregationExecutor(std::unique_ptr<AbstractExecutor> prev,
                       std::vector<TabCol> group_cols,
                       std::vector<ast::AggExpr> agg_exprs,
                       std::vector<Condition> having_conds,
                       std::vector<TabCol> sel_cols) {
        prev_ = std::move(prev);
        group_cols_ = std::move(group_cols);
        agg_exprs_ = std::move(agg_exprs);
        having_conds_ = std::move(having_conds);
        sel_cols_ = std::move(sel_cols);



        // 构建输出列元数据
        build_output_cols();
    }

    void beginTuple() override {
        if (!is_executed_) {
            execute_aggregation();
            is_executed_ = true;
        }
        current_result_ = results_.begin();
    }

    void nextTuple() override {
        if (current_result_ != results_.end()) {
            ++current_result_;
        }
    }

    bool is_end() const override {
        // 对于空表的聚合查询，如果没有结果，直接返回true（不输出任何数据行）
        if (results_.empty()) {
            return true;
        }
        return current_result_ == results_.end();
    }

    std::unique_ptr<RmRecord> Next() override {
        if (is_end()) {
            return nullptr;
        }
        
        // 构建输出记录
        auto record = std::make_unique<RmRecord>(tuple_len_);
        char* data = record->data;
        
        const GroupKey& group_key = current_result_->first;
        const AggregateResult& agg_result = current_result_->second;
        
        size_t offset = 0;
        
        // 填充分组列的值
        for (size_t i = 0; i < group_cols_.size(); ++i) {
            const Value& val = group_key.key_values[i];
            write_value_to_record(data + offset, val, output_cols_[i]);
            offset += output_cols_[i].len;
        }
        
        // 填充聚合函数的值
        for (size_t i = 0; i < agg_exprs_.size(); ++i) {
            const ast::AggExpr& agg_expr = agg_exprs_[i];
            std::string key = get_agg_key(agg_expr);
            
            auto it = agg_result.values.find(key);
            if (it != agg_result.values.end()) {
                size_t col_idx = group_cols_.size() + i;
                write_value_to_record(data + offset, it->second, output_cols_[col_idx]);
                offset += output_cols_[col_idx].len;
            }
        }
        
        return record;
    }

    const std::vector<ColMeta>& cols() const override {
        return output_cols_;
    }

    Rid& rid() override {
        return _abstract_rid;
    }

private:
    void build_output_cols() {
        output_cols_.clear();
        size_t offset = 0;

        // 添加分组列
        for (const auto& group_col : group_cols_) {
            ColMeta col_meta = prev_->get_col_offset(group_col);
            col_meta.offset = offset;
            output_cols_.push_back(col_meta);
            offset += col_meta.len;
        }

        // 添加聚合函数列
        for (const auto& agg_expr : agg_exprs_) {
            ColMeta col_meta;
            col_meta.tab_name = "";
            col_meta.name = agg_expr.alias.empty() ? get_agg_key(agg_expr) : agg_expr.alias;
            col_meta.offset = offset;

            // 根据聚合函数类型确定输出类型
            switch (agg_expr.type) {
                case AGG_COUNT:
                    col_meta.type = TYPE_INT;
                    col_meta.len = sizeof(int);
                    break;
                case AGG_SUM:
                    // SUM应该保持原列的类型
                    if (agg_expr.col) {
                        ColMeta source_col = prev_->get_col_offset({agg_expr.col->tab_name, agg_expr.col->col_name});
                        col_meta.type = source_col.type;
                        col_meta.len = source_col.len;
                    } else {
                        col_meta.type = TYPE_INT;
                        col_meta.len = sizeof(int);
                    }
                    break;
                case AGG_AVG:
                    // AVG始终返回float类型
                    col_meta.type = TYPE_FLOAT;
                    col_meta.len = sizeof(float);
                    break;
                case AGG_MAX:
                case AGG_MIN:
                    if (agg_expr.col) {
                        ColMeta source_col = prev_->get_col_offset({agg_expr.col->tab_name, agg_expr.col->col_name});
                        col_meta.type = source_col.type;
                        col_meta.len = source_col.len;
                    } else {
                        col_meta.type = TYPE_INT;
                        col_meta.len = sizeof(int);
                    }
                    break;
                default:
                    col_meta.type = TYPE_INT;
                    col_meta.len = sizeof(int);
                    break;
            }

            output_cols_.push_back(col_meta);
            offset += col_meta.len;
        }

        tuple_len_ = offset;
    }

    void execute_aggregation();
    GroupKey extract_group_key(const std::unique_ptr<RmRecord>& record);
    void update_aggregate_state(AggregateState& state, const ast::AggExpr& agg_expr,
                               const std::unique_ptr<RmRecord>& record);
    AggregateResult compute_final_result(const AggregateState& state);
    bool evaluate_having_conditions(const GroupKey& group_key, const AggregateResult& agg_result);
    std::string get_agg_key(const ast::AggExpr& agg_expr);
    void write_value_to_record(char* dest, const Value& value, const ColMeta& col_meta);
    Value extract_column_value(const std::unique_ptr<RmRecord>& record, const TabCol& col);
    int compare_values(const Value& v1, const Value& v2);
    bool evaluate_condition(const Value& lhs, const Value& rhs, CompOp op);
};
