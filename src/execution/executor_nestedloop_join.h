/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class NestedLoopJoinExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> left_;    // 左儿子节点（需要join的表）
    std::unique_ptr<AbstractExecutor> right_;   // 右儿子节点（需要join的表）
    size_t len_;                                // join后获得的每条记录的长度
    std::vector<ColMeta> cols_;                 // join后获得的记录的字段

    std::vector<Condition> fed_conds_;          // join条件
    bool isend;
    std::unique_ptr<RmRecord> left_record;

   public:
    NestedLoopJoinExecutor(std::unique_ptr<AbstractExecutor> left, std::unique_ptr<AbstractExecutor> right, 
                            std::vector<Condition> conds) {
        /*左右子执行器*/
        left_ = std::move(left);
        right_ = std::move(right);
        /*连接后的元组长度*/
        len_ = left_->tupleLen() + right_->tupleLen();
        cols_ = left_->cols();
        auto right_cols = right_->cols();
        /*右元组位移*/
        for (auto &col : right_cols) {
            col.offset += left_->tupleLen();
        }
        /*合并左右元组*/
        cols_.insert(cols_.end(), right_cols.begin(), right_cols.end());
        isend = false;
        fed_conds_ = std::move(conds);

    }

    void beginTuple() override {
        /*1.把左、右执行器移到第一个元组*/
        left_->beginTuple();
        right_->beginTuple();
        isend = right_->is_end();
        /*2.把左执行器移到第一个满足条件的元组*/
        left_record = left_->Next();
    }

    void nextTuple() override {
        /*1.把左执行器移到下一个元组*/
        /*1.1若右执行器未结束则不动*/
        right_->nextTuple();
        isend = right_->is_end();
        if(isend)
        {
            /*左执行器移动到下一个元组*/
            left_->nextTuple();
            left_record = left_->Next();
            /*重启右执行器*/
            right_->beginTuple();
            isend = right_->is_end();
        }
    }

    std::unique_ptr<RmRecord> Next() override {
        /*1.一次返回连接好的一个元组*/
        auto record = std::make_unique<RmRecord>(len_);
        while(!isend && !left_->is_end() && left_record)
        {
            /*1.1右执行器还有元组，那么依次遍历剩下的元组*/
            std::unique_ptr<RmRecord> right_record = right_->Next();
            if(right_record)
            {
                /*1.2判断是否连接成功*/
                bool isjoin = true;
                for(auto& cond : fed_conds_)
                {
                    if(!cond.RecordIsEqual(*left_record,*right_record,left_->cols(),right_->cols()))
                    {
                        isjoin = false;
                        break;
                    }
                }
                /*1.3如果连接成功则返回*/
                if(isjoin)
                {
                    memcpy(record->data, left_record->data, left_->tupleLen());
                    memcpy(record->data + left_->tupleLen(), right_record->data, right_->tupleLen());

                    return record;
                }
                /*1.4当前元组连接失败，右子执行器移动到下一个元组*/
                else
                {
                    right_->nextTuple();
                    isend = right_->is_end();
                    if(isend)
                    {
                        /*1.5如果右子执行器到了结尾则重启右子执行器，同时左子执行器移动到下一个元组*/
                        left_->nextTuple();
                        left_record = left_->Next();
                        right_->beginTuple();
                        isend = right_->is_end();
                    }
                }
            }
            /*右执行器到末尾了*/
            else
            {
                left_->nextTuple();
                left_record = left_->Next();
                right_->beginTuple();
                isend = right_->is_end();
            }

        }
        return nullptr;
    }

    const std::vector<ColMeta> &cols() const override{
        // std::vector<ColMeta> *_cols = nullptr;
        return cols_;
    };

    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        return left_->is_end();
    }
    Rid &rid() override { return _abstract_rid; }
};