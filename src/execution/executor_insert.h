/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class InsertExecutor : public AbstractExecutor {
   private:
    TabMeta tab_;                   // 表的元数据
    std::vector<Value> values_;     // 需要插入的数据
    RmFileHandle *fh_;              // 表的数据文件句柄
    std::string tab_name_;          // 表名称
    Rid rid_;                       // 插入的位置，由于系统默认插入时不指定位置，因此当前rid_在插入后才赋值
    SmManager *sm_manager_;

   public:
    InsertExecutor(SmManager *sm_manager, const std::string &tab_name, std::vector<Value> values, Context *context) {
        sm_manager_ = sm_manager;
        tab_ = sm_manager_->db_.get_table(tab_name);
        values_ = values;
        tab_name_ = tab_name;
        if (values.size() != tab_.cols.size()) {
            throw InvalidValueCountError();
        }
        fh_ = sm_manager_->fhs_.at(tab_name).get();
        context_ = context;
    };

    std::unique_ptr<RmRecord> Next() override {
    // Make record buffer
    RmRecord rec(fh_->get_file_hdr().record_size);
    for (size_t i = 0; i < values_.size(); i++) {
        auto &col = tab_.cols[i];
        auto &val = values_[i];
        if (col.type != val.type) {
            if ((col.type == TYPE_STRING && val.type != TYPE_STRING) ||
                (col.type != TYPE_STRING && val.type == TYPE_STRING)) {
                throw IncompatibleTypeError(coltype2str(col.type), coltype2str(val.type));
            } else if (col.type == TYPE_INT && val.type == TYPE_FLOAT) {
                val.type = TYPE_INT;
                val.set_int(int(val.float_val));
            } else {
                val.type = TYPE_FLOAT;
                val.set_float(float(val.int_val));
            }
        }
        val.init_raw(col.len);
        memcpy(rec.data + col.offset, val.raw->data, col.len);
    }

    // 构造 key 并尝试插入所有索引（预演），失败则提前报错
    std::vector<std::pair<IxIndexHandle*, std::unique_ptr<char[]>>> pending_keys;
    for (const auto& index : tab_.indexes) {
        auto ih = sm_manager_->ihs_.at(sm_manager_->get_ix_manager()->get_index_name(tab_name_, index.cols)).get();
        auto key = std::make_unique<char[]>(index.col_tot_len);
        int offset = 0;
        for (int i = 0; i < index.col_num; ++i) {
            memcpy(key.get() + offset, rec.data + index.cols[i].offset, index.cols[i].len);
            offset += index.cols[i].len;
        }

        // 索引先插入，插入失败直接抛异常
        ih->insert_entry(key.get(), Rid{-1, -1}, context_->txn_);  // 临时插入尝试（-1,-1 可用作测试）
        ih->delete_entry(key.get(), context_->txn_);  // 删除临时记录
        pending_keys.emplace_back(ih, std::move(key));
    }

    // 所有索引均插入成功，开始正式插入记录
    rid_ = fh_->insert_record(rec.data, context_);

    // 重新写入真实 rid 到索引中
    for (auto &[ih, key] : pending_keys) {
        // ih->delete_entry(key.get(), context_->txn_);  // 删除临时记录
        ih->insert_entry(key.get(), rid_, context_->txn_);         // 插入真实 rid
    }
    /*7.写事务的写集合*/
    WriteRecord *wr = new WriteRecord(WType::INSERT_TUPLE, tab_name_, rid_, rec);
    context_->txn_->append_write_record(wr);
    return nullptr;
}

    Rid &rid() override { return rid_; }
};

