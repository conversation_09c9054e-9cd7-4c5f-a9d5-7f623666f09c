/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class ProjectionExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;        // 投影节点的儿子节点
    std::vector<ColMeta> cols_;                     // 需要投影的字段
    size_t len_;                                    // 字段总长度
    std::vector<size_t> sel_idxs_;                  

   public:
    ProjectionExecutor(std::unique_ptr<AbstractExecutor> prev, const std::vector<TabCol> &sel_cols) {
        prev_ = std::move(prev);

        size_t curr_offset = 0;
        auto &prev_cols = prev_->cols();
        for (auto &sel_col : sel_cols) {
            /*prev_cols是子执行器返回的列，sel_cols是目标投影列*/
            auto pos = get_col(prev_cols, sel_col);
            /*记录该目标列在prev_cols中的偏移*/
            sel_idxs_.push_back(pos - prev_cols.begin());
            /*构造目标列*/
            auto col = *pos;
            col.offset = curr_offset;
            curr_offset += col.len;
            cols_.push_back(col);
        }
        len_ = curr_offset;
    }

    void beginTuple() override {
        /*1.将子执行器移动到第一个元组*/
        prev_->beginTuple();
    }

    void nextTuple() override {
        /*1.移动到下一个元组*/
        prev_->nextTuple();
    }

    std::unique_ptr<RmRecord> Next() override {
        /*1.从子执行器获取一个元组*/
        std::unique_ptr<RmRecord> record = prev_->Next();
        if(record)
        {
            /*2.对该元组进行投影*/
            auto newRecord = std::make_unique<RmRecord>(len_);
            size_t offset = 0;
            for(size_t i = 0; i < sel_idxs_.size(); i++)
            {
                size_t sel_index = sel_idxs_[i];
                auto &prev_cols = prev_->cols();
                ColMeta col = prev_cols[sel_index];
                memcpy(newRecord->data + offset, record->data + col.offset, col.len);
                offset += col.len;
            }
            return newRecord;
        }
        return nullptr;
    }

    const std::vector<ColMeta> &cols() const override
    {
        return cols_;
    }
    size_t tupleLen() const override{ return len_; };
    bool is_end() const override{
        return prev_->is_end();
    }

    Rid &rid() override { return _abstract_rid; }
};