/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"

class SortExecutor : public AbstractExecutor {
   private:
    std::unique_ptr<AbstractExecutor> prev_;
    // ColMeta cols_;                              // 框架中只支持一个键排序，需要自行修改数据结构支持多个键排序
    
    
    std::vector<ColMeta> cols_;                 // 框架中只支持一个键排序，需要自行修改数据结构支持多个键排序
    size_t tuple_num;
    std::vector<bool> is_col_desc;
    /*用来放第i个元组在records中的下标，假如第一个值是3，代表第一个元组是records[3]*/
    std::vector<size_t> used_tuple;
    std::unique_ptr<RmRecord> current_tuple;
    size_t current_index;
    /*用来放子执行器的所有元组*/
    std::vector<std::unique_ptr<RmRecord>> records;

    // LIMIT相关
    int limit_;                                 // LIMIT数量，-1表示无限制
    size_t effective_tuple_num;                 // 考虑LIMIT后的有效元组数量

   public:
    SortExecutor(std::unique_ptr<AbstractExecutor> prev, std::vector<TabCol> sel_cols, std::vector<bool> is_desc, int limit = -1) {
        prev_ = std::move(prev);
        std::vector<TabCol>::iterator it = sel_cols.begin();
        while(it != sel_cols.end())
        {
            cols_.push_back(prev_->get_col_offset(*it));
            it++;
        }
        // cols_ = prev_->get_col_offset(sel_cols);
        is_col_desc = is_desc;
        tuple_num = 0;
        used_tuple.clear();
        limit_ = limit;
        effective_tuple_num = 0;
    }

    void beginTuple() override {
        /*1.把所有的元组取回来*/
        for(prev_->beginTuple();!prev_->is_end();prev_->nextTuple())
        {
            auto tuple = prev_->Next();
            if(tuple)
            {
                records.push_back(std::move(tuple));
                used_tuple.push_back(tuple_num++);
            }
        }
        current_index = 0;

        /*2.对所有元组进行排序，不改变原来记录的位置*/
        auto compare = [this](size_t a_idx, size_t b_idx) {
        const char* data_a = records[a_idx]->data;
        const char* data_b = records[b_idx]->data;

        // 遍历所有排序列
        int i = 0;
        bool is_desc_ = false;
        for (const auto& col : cols_) {
            const char* val_a = data_a + col.offset;
            const char* val_b = data_b + col.offset;
            is_desc_ = is_col_desc[i];
            i++;
            // 根据列类型比较
            switch (col.type) {
                case TYPE_INT: {
                    int int_a = *reinterpret_cast<const int*>(val_a);
                    int int_b = *reinterpret_cast<const int*>(val_b);
                    if (int_a != int_b) {
                        return is_desc_ ? int_a > int_b : int_a < int_b;
                    }
                    break;  // 相等则继续比较下一列
                }
                case TYPE_FLOAT: {
                    float float_a = *reinterpret_cast<const float*>(val_a);
                    float float_b = *reinterpret_cast<const float*>(val_b);
                    if (std::fabs(float_a - float_b) > 1e-6f) {
                        return is_desc_ ? float_a > float_b : float_a < float_b;
                    }
                    break;
                }
                case TYPE_STRING: {
                    int cmp = memcmp(val_a, val_b, col.len);
                    if (cmp != 0) {
                        return is_desc_ ? cmp > 0 : cmp < 0;
                    }
                    break;
                }
                default:
                    throw std::runtime_error("Unsupported column type: " + 
                                            std::to_string(col.type));
            }
            }
            return false;  // 所有列都相等
        };
        std::sort(used_tuple.begin(), used_tuple.end(), compare);

        // 应用LIMIT
        if (limit_ > 0 && limit_ < static_cast<int>(tuple_num)) {
            effective_tuple_num = limit_;
        } else {
            effective_tuple_num = tuple_num;
        }
    }

    void nextTuple() override {
        current_index++;
    }

    std::unique_ptr<RmRecord> Next() override {
        current_tuple = std::move(records[used_tuple[current_index]]);
        return std::move(current_tuple);
    }

    const std::vector<ColMeta> &cols() const override
    {
        return prev_->cols();
    }

    bool is_end() const override{
        return current_index == effective_tuple_num;
    }

    Rid &rid() override { return _abstract_rid; }
};


        // /*2.对所有元组进行排序，不改变原来记录的位置*/
        // auto compare = [this](size_t a_idx, size_t b_idx) {
        // const char* data_a = records[a_idx]->data;
        // const char* data_b = records[b_idx]->data;

        // // 遍历所有排序列
        // for (const auto& col : cols_) {
        //     const char* val_a = data_a + col.offset;
        //     const char* val_b = data_b + col.offset;

        //     // 根据列类型比较
        //     switch (col.type) {
        //         case TYPE_INT: {
        //             int int_a = *reinterpret_cast<const int*>(val_a);
        //             int int_b = *reinterpret_cast<const int*>(val_b);
        //             if (int_a != int_b) {
        //                 return is_desc_ ? int_a > int_b : int_a < int_b;
        //             }
        //             break;  // 相等则继续比较下一列
        //         }
        //         case TYPE_FLOAT: {
        //             float float_a = *reinterpret_cast<const float*>(val_a);
        //             float float_b = *reinterpret_cast<const float*>(val_b);
        //             if (std::fabs(float_a - float_b) > 1e-6f) {
        //                 return is_desc_ ? float_a > float_b : float_a < float_b;
        //             }
        //             break;
        //         }
        //         case TYPE_STRING: {
        //             int cmp = memcmp(val_a, val_b, col.len);
        //             if (cmp != 0) {
        //                 return is_desc_ ? cmp > 0 : cmp < 0;
        //             }
        //             break;
        //         }
        //         default:
        //             throw std::runtime_error("Unsupported column type: " + 
        //                                     std::to_string(col.type));
        //     }
        //     }
        //     return false;  // 所有列都相等
        // };


        //         // 执行排序（原地修改索引映射）
        // auto compare = [this](size_t a_idx, size_t b_idx) {
        //     const char* data_a = records[a_idx]->data + cols_.offset;
        //     const char* data_b = records[b_idx]->data + cols_.offset;
            
        //     switch (cols_.type) {
        //         case TYPE_INT: {
        //             int val_a = *reinterpret_cast<const int*>(data_a);
        //             int val_b = *reinterpret_cast<const int*>(data_b);
        //             return is_desc_ ? val_a > val_b : val_a < val_b;
        //         }
        //         case TYPE_FLOAT: {
        //             float val_a = *reinterpret_cast<const float*>(data_a);
        //             float val_b = *reinterpret_cast<const float*>(data_b);
        //             return is_desc_ ? val_a > val_b : val_a < val_b;
        //         }
        //         case TYPE_STRING: {
        //             int cmp = strncmp(data_a, data_b, cols_.len);
        //             return is_desc_ ? cmp > 0 : cmp < 0;
        //         }
        //         default:
        //             throw std::runtime_error("Unsupported column type for sorting");
        //     }
        // };