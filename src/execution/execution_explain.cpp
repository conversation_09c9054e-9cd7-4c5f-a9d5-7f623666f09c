/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "execution_explain.h"

// 辅助函数：生成缩进字符串
std::string get_indent(int level) {
    std::string indent;
    for (int i = 0; i < level; ++i) {
        indent += "\t";
    }
    return indent;
}

// 辅助函数：按字典序排序列名
bool compare_col_names(const TabCol& a, const TabCol& b) {
    std::string full_a = a.tab_name + "." + a.col_name;
    std::string full_b = b.tab_name + "." + b.col_name;
    return full_a < full_b;
}

// 辅助函数：按字典序排序列表
template<typename T>
void sort_lexicographically(std::vector<T>& list) {
    std::sort(list.begin(), list.end());
}

// 收集Join节点及其子节点中的所有表名
void collect_tables(const std::shared_ptr<Plan>& plan, std::vector<std::string>& tables) {
    if (auto scan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
        tables.push_back(scan->tab_name_);
    } else if (auto join = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        if (join->left_) collect_tables(join->left_, tables);
        if (join->right_) collect_tables(join->right_, tables);
    }
    else if (auto projection = std::dynamic_pointer_cast<ProjectionPlan>(plan))
    {
        if(projection->subplan_)
        {
            collect_tables(projection->subplan_, tables);
        }
    }
    // 其他类型节点可忽略（如Filter、）
}

// 辅助函数：操作符转字符串
std::string op_to_string(CompOp op) {
    switch (op) {
        case OP_EQ: return "=";
        case OP_NE: return "!=";
        case OP_LT: return "<";
        case OP_GT: return ">";
        case OP_LE: return "<=";
        case OP_GE: return ">=";
        default: return "?";
    }
}

// 辅助函数：Value转字符串（修正版）
std::string value_to_string(const Value& val) {
    std::stringstream ss;
    switch (val.type) {
        case TYPE_INT:
            ss << val.int_val;
            break;
        case TYPE_FLOAT:
            // 保留6位小数
            ss << std::fixed << std::setprecision(6) << val.float_val;
            break;
        case TYPE_STRING:
            // 字符串添加单引号
            ss << "'" << val.str_val << "'";
            break;
        default:
            ss << "?";
    }
    return ss.str();
}

// 计划节点比较函数（核心排序逻辑）
bool compare_plans(const std::shared_ptr<Plan>& a, const std::shared_ptr<Plan>& b) {
    // 1. 按节点类型排序：Filter → Join → Project → Scan
    // 注意：Filter实际是带有条件的Scan节点，需要特殊处理
    static std::map<PlanTag, int> type_order = {
        {T_NestLoop, 1},    // Join
        {T_Projection, 2},  // Project
        {T_SeqScan, 3}      // Scan (无过滤条件的Scan)
    };

    // 判断节点是否为带有过滤条件的Scan节点（即Filter节点）
    auto is_filter_scan = [](const std::shared_ptr<Plan>& plan) {
        if (auto scan = std::dynamic_pointer_cast<ScanPlan>(plan)) {
            return !scan->fed_conds_.empty();
        }
        return false;
    };

    bool is_filter_a = is_filter_scan(a);
    bool is_filter_b = is_filter_scan(b);

    // 如果有Filter节点，优先排序
    if (is_filter_a != is_filter_b) {
        return is_filter_a; // Filter 优先级最高
    }

    // 两个都是Filter或都不是Filter的情况
    PlanTag effective_tag_a = is_filter_a ? T_SeqScan : a->tag;
    PlanTag effective_tag_b = is_filter_b ? T_SeqScan : b->tag;

    if (effective_tag_a != effective_tag_b) {
        return type_order[effective_tag_a] < type_order[effective_tag_b];
    }

    // 2. 同类型节点按具体规则排序
    switch (effective_tag_a) {
        case T_SeqScan: {
            if (is_filter_a && is_filter_b) {
                // 两个都是Filter节点，按条件字典序排序
                auto scan_a = std::dynamic_pointer_cast<ScanPlan>(a);
                auto scan_b = std::dynamic_pointer_cast<ScanPlan>(b);
                
                std::vector<std::string> conds_a, conds_b;
                for (const auto& cond : scan_a->fed_conds_) {
                    conds_a.push_back(cond.lhs_col.tab_name + "." + cond.lhs_col.col_name + 
                                     op_to_string(cond.op) + value_to_string(cond.rhs_val));
                }
                for (const auto& cond : scan_b->fed_conds_) {
                    conds_b.push_back(cond.lhs_col.tab_name + "." + cond.lhs_col.col_name + 
                                     op_to_string(cond.op) + value_to_string(cond.rhs_val));
                }
                sort_lexicographically(conds_a);
                sort_lexicographically(conds_b);
                return conds_a < conds_b;
            } else if (!is_filter_a && !is_filter_b) {
                // 两个都是普通Scan节点，按表名字典序排序
                auto scan_a = std::dynamic_pointer_cast<ScanPlan>(a);
                auto scan_b = std::dynamic_pointer_cast<ScanPlan>(b);
                return scan_a->tab_name_ < scan_b->tab_name_;
            } else {
                // 不可能同时满足一个是Filter一个不是（前面已处理）
                return false;
            }
        }
        case T_NestLoop: {
            auto join_a = std::dynamic_pointer_cast<JoinPlan>(a);
            auto join_b = std::dynamic_pointer_cast<JoinPlan>(b);
            std::vector<std::string> tables_a, tables_b;
            collect_tables(a, tables_a);
            collect_tables(b, tables_b);
            sort_lexicographically(tables_a);
            sort_lexicographically(tables_b);
            return tables_a < tables_b;
        }
        case T_Projection: {
            // 保持投影节点的原有顺序
            return false;
        }
        default: return false;
    }
}

// 递归生成执行计划树的字符串表示
std::string explain_plan(const std::shared_ptr<Plan>& plan, int level = 0) {
    std::stringstream ss;
    std::string indent = get_indent(level);
    
    /*1.根据查询计划格式化输出*/
    if(auto dml_plan = std::dynamic_pointer_cast<DMLPlan>(plan))
    {
        ss << explain_plan(dml_plan->subplan_, level);
    } else if (auto scan = std::dynamic_pointer_cast<ScanPlan>(plan)) 
    {
        
        /*1.1Filter节点(过滤条件在扫描计划中)*/
        std::vector<std::string> conditions;
        for (const auto& cond : scan->fed_conds_) {
            std::string cond_str = (cond.lhs_col.have_alia ? cond.lhs_col.alia_name : cond.lhs_col.tab_name) + "." + cond.lhs_col.col_name;
            cond_str += op_to_string(cond.op);
            cond_str += value_to_string(cond.rhs_val);
            conditions.push_back(cond_str);
        }

        if(!conditions.empty())
        {
            sort_lexicographically(conditions);
            ss << indent << "Filter(condition=[";
            for (size_t i = 0; i < conditions.size(); ++i) {
                ss << conditions[i];
                if (i < conditions.size() - 1) ss << ",";
            }
            ss << "])\n";

            /*1.2Scan节点*/
            indent = get_indent(level + 1);
            ss << indent << "Scan(table=" << scan->tab_name_ << ")\n";
        }
        else
        {
            ss << indent << "Scan(table=" << scan->tab_name_ << ")\n";
        }



    } else if (auto project = std::dynamic_pointer_cast<ProjectionPlan>(plan)) {
        
        /*1.3Project节点*/
        ss << indent << "Project(columns=[";
        if (plan->is_select_all_cols) {
            ss << "*";
        } else {
            std::vector<TabCol> sorted_cols = project->sel_cols_;
            std::sort(sorted_cols.begin(), sorted_cols.end(), compare_col_names);
            
            for (size_t i = 0; i < sorted_cols.size(); ++i) {
                ss << (sorted_cols[i].have_alia ? sorted_cols[i].alia_name : sorted_cols[i].tab_name) << "." << sorted_cols[i].col_name;
                if (i < sorted_cols.size() - 1) ss << ",";
            }
        }
        ss << "])\n";
        
        /*1.4递归处理子计划*/
        if (project->subplan_) {
            ss << explain_plan(project->subplan_, level + 1);
        }
    } else if (auto join = std::dynamic_pointer_cast<JoinPlan>(plan)) {
        /*1.5Join节点*/
        /*1.5.1收集所有表名并按字典排序*/
        std::vector<std::string> tables;
        collect_tables(join, tables);
        sort_lexicographically(tables);
        
        /*1.6收集并排序连接条件*/
        std::vector<std::string> conditions;
        for (const auto& cond : join->conds_) {
            std::string cond_str = (cond.lhs_col.have_alia ? cond.lhs_col.alia_name : cond.lhs_col.tab_name)+ "." + cond.lhs_col.col_name;
            cond_str += op_to_string(cond.op);
            cond_str += (cond.rhs_col.have_alia ? cond.rhs_col.alia_name : cond.rhs_col.tab_name) + "." + cond.rhs_col.col_name;
            conditions.push_back(cond_str);
        }
        sort_lexicographically(conditions);

        /*1.7笛卡尔积*/
        if(conditions.empty())
        {
            //TODO:如何输出
        }
        
        ss << indent << "Join(tables=[";
        for (size_t i = 0; i < tables.size(); ++i) {
            ss << tables[i];
            if (i < tables.size() - 1) ss << ",";
        }
        ss << "],condition=[";
        for (size_t i = 0; i < conditions.size(); ++i) {
            ss << conditions[i];
            if (i < conditions.size() - 1) ss << ",";
        }
        ss << "])\n";
        
        /*1.8处理子计划*/
        std::vector<std::shared_ptr<Plan>> children;
        if (join->left_) children.push_back(join->left_);
        if (join->right_) children.push_back(join->right_);
        std::sort(children.begin(), children.end(), compare_plans);
        
        for (const auto& child : children) {
            ss << explain_plan(child, level + 1);
        }

        
        // if (join->left_) {
        //     ss << explain_plan(join->left_, level + 1);
        // }
        // if (join->right_) {
        //     ss << explain_plan(join->right_, level + 1);
        // }
        
    } else {
        ss << indent << "UnknownPlanType\n";
    }
    
    return ss.str();
}

std::unique_ptr<RmRecord> ExplainExecutor::Next() {
    if (has_output_) {
        return nullptr;  // 只输出一次执行计划
    }
    std::fstream outfile;
    outfile.open("output.txt", std::ios::out | std::ios::app);
    
    // 生成并打印执行计划
    std::string plan_str = explain_plan(plan_);
    outfile << plan_str;

    size_t buffer_size = 8192;  // 假设 Context 有一个字段表示缓冲区大小
    size_t copy_size = std::min(plan_str.length(), buffer_size - 1);
    
    std::strncpy(context_->data_send_, plan_str.c_str(), copy_size);
    context_->data_send_[copy_size] = '\0';  // 确保字符串以空字符结尾
    *(context_->offset_) = copy_size + 1;
    
    has_output_ = true;
    return nullptr;  // Explain不返回实际记录
}