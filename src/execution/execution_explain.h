/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once
#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "record_printer.h"

class ExplainExecutor : public AbstractExecutor {
private:
    std::shared_ptr<Plan> plan_;       // 被解释的执行计划
    SmManager *sm_manager_;
    bool has_output_ = false;          // 是否已输出执行计划

public:
    ExplainExecutor(SmManager *sm_manager, std::shared_ptr<Plan> plan, Context *context)
    {
        plan_ = plan;
        context_ = context;
        sm_manager_ = sm_manager;
    }

    std::unique_ptr<RmRecord> Next() override;
    Rid &rid() override { throw std::runtime_error("ExplainExecutor does not return records"); }
};