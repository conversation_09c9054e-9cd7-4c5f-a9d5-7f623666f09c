# 数据一致性检验规则
## Consistency check for district, orders and new_orders
- 执行如下SQL:
```sql
SELECT d_next_o_id FROM district WHERE d_w_id = :w_id AND d_id = :d_id;
SELECT MAX(o_id) AS max_o_id FROM orders WHERE o_w_id = :w_id AND o_d_id = :d_id;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id = :w_id AND no_d_id = :d_id;
```
要求:​​
对于每个 (w_id, d_id)，需满足：d_next_o_id - 1 = max(o_id)= max(no_o_id)
## Consistency check for new_orders 
- 执行如下SQL:
```sql
SELECT COUNT(no_o_id) AS count_no_o_id FROM new_orders WHERE no_w_id = :w_id AND no_d_id = :d_id;
SELECT MAX(no_o_id) AS max_no_o_id FROM new_orders WHERE no_w_id = :w_id AND no_d_id = :d_id;
SELECT MIN(no_o_id) AS min_no_o_id FROM new_orders WHERE no_w_id = :w_id AND no_d_id = :d_id;
```
要求：
对于每个 (w_id, d_id)，需满足：count(no_o_id) = max(no_o_id) - min(no_o_id) + 1
## Consistency check for orders and order_line
- 执行如下SQL:
```sql
SELECT SUM(o_ol_cnt) AS sum_ol_cnt FROM orders WHERE o_w_id = :w_id AND o_d_id = :d_id;
SELECT COUNT(ol_o_id) AS count_ol_o_id FROM order_line WHERE ol_w_id = :w_id AND ol_d_id = :d_id;
```
要求：
对于每个 (w_id, d_id)，需满足：sum(o_ol_cnt) = count(ol_o_id)