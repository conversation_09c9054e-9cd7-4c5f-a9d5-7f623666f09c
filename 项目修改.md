# RMDB项目修改记录
## (1)src/optimizer/plan.h
1. 20行后添加
```c++
// Forward declarations
class SmManager;
struct Condition;
struct ColMeta;
struct TabCol;
struct Value;
struct SetClause;
struct ColDef;
struct TabMeta;
```
2. 45行后添加
```c++
T_Aggregation 
```
3. 201行后添加
```c++
// Aggregation Plan for GROUP BY, HAVING and aggregate functions
class AggregationPlan : public Plan
{
    public:
        AggregationPlan(std::shared_ptr<Plan> subplan,
                       std::vector<TabCol> group_cols,
                       std::vector<ast::AggExpr> agg_exprs,
                       std::vector<Condition> having_conds,
                       std::vector<TabCol> sel_cols)
        {
            Plan::tag = T_Aggregation;
            subplan_ = std::move(subplan);
            group_cols_ = std::move(group_cols);
            agg_exprs_ = std::move(agg_exprs);
            having_conds_ = std::move(having_conds);
            sel_cols_ = std::move(sel_cols);
        }
        ~AggregationPlan(){}

        std::shared_ptr<Plan> subplan_;              // 子计划（通常是扫描或连接）
        std::vector<TabCol> group_cols_;             // GROUP BY 列
        std::vector<ast::AggExpr> agg_exprs_;        // 聚合表达式
        std::vector<Condition> having_conds_;        // HAVING 条件
        std::vector<TabCol> sel_cols_;               // 选择列（包括普通列和聚合列）
};
```

## (2)src/optimizer/planner.cpp
1. 265行后修改
- 修改generate_sort_plan的前四行
```c++
// 检查是否是SelectStmt
    auto select_stmt = std::dynamic_pointer_cast<ast::SelectStmt>(query->parse);
    if (select_stmt) {
        if (!select_stmt->has_sort) {
            return plan;
        }
    } else {
        // 检查是否是GroupByStmt
        auto group_stmt = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse);
        if (group_stmt) {
            if (!group_stmt->has_sort) {
                return plan;
            }
        } else {
            // 既不是SelectStmt也不是GroupByStmt，直接返回
            return plan;
        }
    }
```
2. 294行后修改

```c++
// 获取排序信息
    std::vector<std::shared_ptr<ast::OrderBy>> order_list;
    if (select_stmt) {
        order_list = select_stmt->order;
    } else {
        auto group_stmt = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse);
        if (group_stmt) {
            order_list = group_stmt->order;
        }
    }

    for(const auto &od : order_list)//此处为修改部分，上面是添加部分
```
3. 393行后添加
```c++
} else if (auto x = std::dynamic_pointer_cast<ast::GroupByStmt>(query->parse)) {
        // 生成聚合查询的执行计划
        std::shared_ptr<Plan> aggregation_plan = generate_aggregation_plan(std::move(query), context);
        plannerRoot = std::make_shared<DMLPlan>(T_select, aggregation_plan, std::string(), std::vector<Value>(),
                                                    std::vector<Condition>(), std::vector<SetClause>());
```
4. 398行后添加
```c++
/**
 * @brief 生成聚合查询的执行计划
 *
 * @param query 包含聚合信息的查询对象
 * @param context 上下文
 * @return std::shared_ptr<Plan> 聚合执行计划
 */
std::shared_ptr<Plan> Planner::generate_aggregation_plan(std::shared_ptr<Query> query, Context *context) {
    // 逻辑优化
    query = logical_optimization(std::move(query), context);

    // 生成基础的扫描和连接计划
    std::shared_ptr<Plan> base_plan = physical_optimization(query, context);

    // 创建聚合计划
    std::shared_ptr<Plan> aggregation_plan = std::make_shared<AggregationPlan>(
        std::move(base_plan),
        query->group_cols,
        query->agg_exprs,
        query->having_conds,
        query->cols
    );

    // 处理排序（如果有ORDER BY）
    aggregation_plan = generate_sort_plan(query, std::move(aggregation_plan));

    // 添加投影层
    aggregation_plan = std::make_shared<ProjectionPlan>(T_Projection, std::move(aggregation_plan),
                                                       std::move(query->cols));

    return aggregation_plan;
}
```
## (3)src/optimizer/planner.h
1. 55行后添加
```c++
 std::shared_ptr<Plan> generate_aggregation_plan(std::shared_ptr<Query> query, Context *context);
```

## (4)src/execution/executor_aggregation.h
1. 创建代码
```c++
/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#pragma once

#include "execution_defs.h"
#include "execution_manager.h"
#include "executor_abstract.h"
#include "index/ix.h"
#include "system/sm.h"
#include "parser/ast.h"
#include "common/common.h"
#include <unordered_map>
#include <map>
#include <map>

// 聚合结果存储结构
struct AggregateResult {
    std::map<std::string, Value> values;  // 存储聚合函数的结果，key为别名或函数名
    
    AggregateResult() = default;
};

// 分组键结构，用于GROUP BY
struct GroupKey {
    std::vector<Value> key_values;
    
    GroupKey() = default;
    
    GroupKey(const std::vector<Value>& values) : key_values(values) {}
    
    bool operator<(const GroupKey& other) const {
        if (key_values.size() != other.key_values.size()) {
            return key_values.size() < other.key_values.size();
        }
        
        for (size_t i = 0; i < key_values.size(); ++i) {
            const Value& v1 = key_values[i];
            const Value& v2 = other.key_values[i];
            
            if (v1.type != v2.type) {
                return v1.type < v2.type;
            }
            
            switch (v1.type) {
                case TYPE_INT:
                    if (v1.int_val != v2.int_val) {
                        return v1.int_val < v2.int_val;
                    }
                    break;
                case TYPE_FLOAT:
                    if (std::abs(v1.float_val - v2.float_val) > 1e-6f) {
                        return v1.float_val < v2.float_val;
                    }
                    break;
                case TYPE_STRING:
                    if (v1.str_val != v2.str_val) {
                        return v1.str_val < v2.str_val;
                    }
                    break;
                default:
                    break;
            }
        }
        return false;  // 所有值都相等
    }
};

// 单个聚合函数的状态
struct SingleAggState {
    int count = 0;
    double sum = 0.0;
    Value min_val;
    Value max_val;
    bool has_value = false;

    SingleAggState() = default;
};

// 聚合状态，用于累积计算，为每个聚合函数维护独立状态
struct AggregateState {
    std::map<std::string, SingleAggState> agg_states;  // key是聚合函数的标识符

    AggregateState() = default;
};

class AggregationExecutor : public AbstractExecutor {
private:
    std::unique_ptr<AbstractExecutor> prev_;           // 子执行器
    std::vector<TabCol> group_cols_;                   // GROUP BY 列
    std::vector<ast::AggExpr> agg_exprs_;             // 聚合表达式
    std::vector<Condition> having_conds_;             // HAVING 条件
    std::vector<TabCol> sel_cols_;                    // 选择列
    
    // 执行状态
    std::map<GroupKey, AggregateState> group_states_; // 分组聚合状态
    std::map<GroupKey, AggregateResult> results_;     // 最终结果
    std::map<GroupKey, AggregateResult>::iterator current_result_; // 当前结果迭代器
    bool is_executed_ = false;                        // 是否已执行聚合
    
    // 列信息
    std::vector<ColMeta> output_cols_;                // 输出列元数据
    size_t tuple_len_;                                // 输出元组长度

public:
    AggregationExecutor(std::unique_ptr<AbstractExecutor> prev,
                       std::vector<TabCol> group_cols,
                       std::vector<ast::AggExpr> agg_exprs,
                       std::vector<Condition> having_conds,
                       std::vector<TabCol> sel_cols) {
        prev_ = std::move(prev);
        group_cols_ = std::move(group_cols);
        agg_exprs_ = std::move(agg_exprs);
        having_conds_ = std::move(having_conds);
        sel_cols_ = std::move(sel_cols);
        
        // 构建输出列元数据
        build_output_cols();
    }

    void beginTuple() override {
        if (!is_executed_) {
            execute_aggregation();
            is_executed_ = true;
        }
        current_result_ = results_.begin();
    }

    void nextTuple() override {
        if (current_result_ != results_.end()) {
            ++current_result_;
        }
    }

    bool is_end() const override {
        return current_result_ == results_.end();
    }

    std::unique_ptr<RmRecord> Next() override {
        if (is_end()) {
            return nullptr;
        }
        
        // 构建输出记录
        auto record = std::make_unique<RmRecord>(tuple_len_);
        char* data = record->data;
        
        const GroupKey& group_key = current_result_->first;
        const AggregateResult& agg_result = current_result_->second;
        
        size_t offset = 0;
        
        // 填充分组列的值
        for (size_t i = 0; i < group_cols_.size(); ++i) {
            const Value& val = group_key.key_values[i];
            write_value_to_record(data + offset, val, output_cols_[i]);
            offset += output_cols_[i].len;
        }
        
        // 填充聚合函数的值
        for (size_t i = 0; i < agg_exprs_.size(); ++i) {
            const ast::AggExpr& agg_expr = agg_exprs_[i];
            std::string key = get_agg_key(agg_expr);
            
            auto it = agg_result.values.find(key);
            if (it != agg_result.values.end()) {
                size_t col_idx = group_cols_.size() + i;
                write_value_to_record(data + offset, it->second, output_cols_[col_idx]);
                offset += output_cols_[col_idx].len;
            }
        }
        
        return record;
    }

    const std::vector<ColMeta>& cols() const override {
        return output_cols_;
    }

    Rid& rid() override {
        return _abstract_rid;
    }

private:
    void build_output_cols() {
        output_cols_.clear();
        size_t offset = 0;

        // 添加分组列
        for (const auto& group_col : group_cols_) {
            ColMeta col_meta = prev_->get_col_offset(group_col);
            col_meta.offset = offset;
            output_cols_.push_back(col_meta);
            offset += col_meta.len;
        }

        // 添加聚合函数列
        for (const auto& agg_expr : agg_exprs_) {
            ColMeta col_meta;
            col_meta.tab_name = "";
            col_meta.name = agg_expr.alias.empty() ? get_agg_key(agg_expr) : agg_expr.alias;
            col_meta.offset = offset;

            // 根据聚合函数类型确定输出类型
            switch (agg_expr.type) {
                case AGG_COUNT:
                    col_meta.type = TYPE_INT;
                    col_meta.len = sizeof(int);
                    break;
                case AGG_SUM:
                case AGG_AVG:
                    col_meta.type = TYPE_FLOAT;
                    col_meta.len = sizeof(float);
                    break;
                case AGG_MAX:
                case AGG_MIN:
                    if (agg_expr.col) {
                        ColMeta source_col = prev_->get_col_offset({agg_expr.col->tab_name, agg_expr.col->col_name});
                        col_meta.type = source_col.type;
                        col_meta.len = source_col.len;
                    } else {
                        col_meta.type = TYPE_INT;
                        col_meta.len = sizeof(int);
                    }
                    break;
                default:
                    col_meta.type = TYPE_INT;
                    col_meta.len = sizeof(int);
                    break;
            }

            output_cols_.push_back(col_meta);
            offset += col_meta.len;
        }

        tuple_len_ = offset;
    }

    void execute_aggregation();
    GroupKey extract_group_key(const std::unique_ptr<RmRecord>& record);
    void update_aggregate_state(AggregateState& state, const ast::AggExpr& agg_expr,
                               const std::unique_ptr<RmRecord>& record);
    AggregateResult compute_final_result(const AggregateState& state);
    bool evaluate_having_conditions(const GroupKey& group_key, const AggregateResult& agg_result);
    std::string get_agg_key(const ast::AggExpr& agg_expr);
    void write_value_to_record(char* dest, const Value& value, const ColMeta& col_meta);
    Value extract_column_value(const std::unique_ptr<RmRecord>& record, const TabCol& col);
    int compare_values(const Value& v1, const Value& v2);
    bool evaluate_condition(const Value& lhs, const Value& rhs, CompOp op);
};


```

## (5)src/execution/executor_aggregation.cpp
1. 创建代码
```c++
/* Copyright (c) 2023 Renmin University of China
RMDB is licensed under Mulan PSL v2.
You can use this software according to the terms and conditions of the Mulan PSL v2.
You may obtain a copy of Mulan PSL v2 at:
        http://license.coscl.org.cn/MulanPSL2
THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
See the Mulan PSL v2 for more details. */

#include "executor_aggregation.h"
#include "common/common.h"
#include <algorithm>
#include <cstring>

void AggregationExecutor::execute_aggregation() {
    group_states_.clear();
    results_.clear();

    // 遍历所有输入元组
    for (prev_->beginTuple(); !prev_->is_end(); prev_->nextTuple()) {
        auto record = prev_->Next();
        if (!record) continue;

        // 提取分组键
        GroupKey group_key = extract_group_key(record);

        // 获取或创建该分组的聚合状态
        AggregateState& state = group_states_[group_key];

        // 更新聚合状态
        for (const auto& agg_expr : agg_exprs_) {
            update_aggregate_state(state, agg_expr, record);
        }
    }

    // 处理空结果集的情况：如果没有GROUP BY且没有输入记录，仍需要返回聚合函数的默认值
    if (group_states_.empty() && group_cols_.empty()) {
        // 创建一个空的分组键和默认聚合状态
        GroupKey empty_key(std::vector<Value>{});
        AggregateState default_state;

        // 对于没有输入记录的情况，聚合函数应该返回默认值
        // COUNT(*) 和 COUNT(column) 返回 0
        // SUM, AVG, MAX, MIN 在没有记录时通常返回 NULL，但这里我们简化处理

        AggregateResult result = compute_final_result(default_state);

        // 检查HAVING条件（对于空结果集，通常HAVING条件也应该被评估）
        if (evaluate_having_conditions(empty_key, result)) {
            results_[empty_key] = result;
        }
    } else {
        // 计算最终结果并应用HAVING条件
        for (const auto& pair : group_states_) {
            const GroupKey& group_key = pair.first;
            const AggregateState& state = pair.second;

            AggregateResult result = compute_final_result(state);

            // 检查HAVING条件
            if (evaluate_having_conditions(group_key, result)) {
                results_[group_key] = result;
            }
        }
    }
}

GroupKey AggregationExecutor::extract_group_key(const std::unique_ptr<RmRecord>& record) {
    std::vector<Value> key_values;
    
    for (const auto& group_col : group_cols_) {
        Value val = extract_column_value(record, group_col);
        key_values.push_back(val);
    }
    
    return GroupKey(key_values);
}

void AggregationExecutor::update_aggregate_state(AggregateState& state, const ast::AggExpr& agg_expr,
                                                 const std::unique_ptr<RmRecord>& record) {
    // 获取聚合函数的唯一标识符
    std::string agg_key = get_agg_key(agg_expr);
    SingleAggState& single_state = state.agg_states[agg_key];

    switch (agg_expr.type) {
        case AGG_COUNT:
            if (agg_expr.col == nullptr) {
                // COUNT(*)
                single_state.count++;
            } else {
                // COUNT(column) - 只计算非NULL值
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});
                // 这里简化处理，假设所有值都非NULL
                single_state.count++;
            }
            single_state.has_value = true;
            break;

        case AGG_SUM:
        case AGG_AVG:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});
                double num_val = 0.0;

                if (val.type == TYPE_INT) {
                    num_val = static_cast<double>(val.int_val);
                } else if (val.type == TYPE_FLOAT) {
                    num_val = static_cast<double>(val.float_val);
                }

                single_state.sum += num_val;
                single_state.count++;
                single_state.has_value = true;
            }
            break;

        case AGG_MAX:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});

                if (!single_state.has_value) {
                    single_state.max_val = val;
                    single_state.has_value = true;
                } else {
                    // 比较并更新最大值
                    if (compare_values(val, single_state.max_val) > 0) {
                        single_state.max_val = val;
                    }
                }
            }
            break;

        case AGG_MIN:
            if (agg_expr.col) {
                Value val = extract_column_value(record, {agg_expr.col->tab_name, agg_expr.col->col_name});

                if (!single_state.has_value) {
                    single_state.min_val = val;
                    single_state.has_value = true;
                } else {
                    // 比较并更新最小值
                    if (compare_values(val, single_state.min_val) < 0) {
                        single_state.min_val = val;
                    }
                }
            }
            break;

        default:
            break;
    }
}

AggregateResult AggregationExecutor::compute_final_result(const AggregateState& state) {
    AggregateResult result;

    for (const auto& agg_expr : agg_exprs_) {
        std::string key = get_agg_key(agg_expr);
        Value final_val;

        // 获取该聚合函数的状态
        auto it = state.agg_states.find(key);
        if (it == state.agg_states.end()) {
            // 如果没有找到状态，使用默认值
            final_val.set_int(0);
            result.values[key] = final_val;
            continue;
        }

        const SingleAggState& single_state = it->second;

        switch (agg_expr.type) {
            case AGG_COUNT:
                final_val.set_int(single_state.count);
                break;

            case AGG_SUM:
                final_val.set_float(static_cast<float>(single_state.sum));
                break;

            case AGG_AVG:
                if (single_state.count > 0) {
                    final_val.set_float(static_cast<float>(single_state.sum / single_state.count));
                } else {
                    final_val.set_float(0.0f);
                }
                break;

            case AGG_MAX:
                if (single_state.has_value) {
                    final_val = single_state.max_val;
                } else {
                    final_val.set_int(0);  // 默认值
                }
                break;

            case AGG_MIN:
                if (single_state.has_value) {
                    final_val = single_state.min_val;
                } else {
                    final_val.set_int(0);  // 默认值
                }
                break;

            default:
                final_val.set_int(0);
                break;
        }

        result.values[key] = final_val;
    }

    return result;
}

bool AggregationExecutor::evaluate_having_conditions(const GroupKey& group_key, const AggregateResult& agg_result) {
    // 如果没有HAVING条件，返回true
    if (having_conds_.empty()) {
        return true;
    }
    
    // 评估所有HAVING条件（AND连接）
    for (const auto& cond : having_conds_) {
        Value lhs_val, rhs_val;
        
        // 获取左操作数的值
        if (cond.is_lhs_agg) {
            // 左操作数是聚合函数
            std::string agg_key = get_agg_key(cond.lhs_agg);
            auto it = agg_result.values.find(agg_key);
            if (it != agg_result.values.end()) {
                lhs_val = it->second;
            }
        } else {
            // 左操作数是分组列
            for (size_t i = 0; i < group_cols_.size(); ++i) {
                if (group_cols_[i].tab_name == cond.lhs_col.tab_name && 
                    group_cols_[i].col_name == cond.lhs_col.col_name) {
                    lhs_val = group_key.key_values[i];
                    break;
                }
            }
        }
        
        // 获取右操作数的值
        if (cond.is_rhs_val) {
            rhs_val = cond.rhs_val;
        } else {
            // 右操作数是列，这里简化处理
            // 实际应该从分组键或聚合结果中获取
        }
        
        // 评估条件
        if (!evaluate_condition(lhs_val, rhs_val, cond.op)) {
            return false;
        }
    }
    
    return true;
}

std::string AggregationExecutor::get_agg_key(const ast::AggExpr& agg_expr) {
    if (!agg_expr.alias.empty()) {
        return agg_expr.alias;
    }
    
    std::string func_name;
    switch (agg_expr.type) {
        case AGG_COUNT: func_name = "COUNT"; break;
        case AGG_SUM: func_name = "SUM"; break;
        case AGG_AVG: func_name = "AVG"; break;
        case AGG_MAX: func_name = "MAX"; break;
        case AGG_MIN: func_name = "MIN"; break;
        default: func_name = "UNKNOWN"; break;
    }
    
    if (agg_expr.col) {
        return func_name + "(" + agg_expr.col->col_name + ")";
    } else {
        return func_name + "(*)";
    }
}

void AggregationExecutor::write_value_to_record(char* dest, const Value& value, const ColMeta& col_meta) {
    switch (col_meta.type) {
        case TYPE_INT:
            *reinterpret_cast<int*>(dest) = value.int_val;
            break;
        case TYPE_FLOAT:
            *reinterpret_cast<float*>(dest) = value.float_val;
            break;
        case TYPE_STRING:
            std::memset(dest, 0, col_meta.len);
            std::memcpy(dest, value.str_val.c_str(), std::min(value.str_val.length(), static_cast<size_t>(col_meta.len)));
            break;
        default:
            break;
    }
}

Value AggregationExecutor::extract_column_value(const std::unique_ptr<RmRecord>& record, const TabCol& col) {
    ColMeta col_meta = prev_->get_col_offset(col);
    Value value;
    
    const char* data = record->data + col_meta.offset;
    
    switch (col_meta.type) {
        case TYPE_INT:
            value.set_int(*reinterpret_cast<const int*>(data));
            break;
        case TYPE_FLOAT:
            value.set_float(*reinterpret_cast<const float*>(data));
            break;
        case TYPE_STRING:
            value.set_str(std::string(data, col_meta.len));
            // 去除尾部的空字符
            value.str_val.resize(std::strlen(value.str_val.c_str()));
            break;
        default:
            break;
    }
    
    return value;
}

// 辅助函数：评估条件
bool AggregationExecutor::evaluate_condition(const Value& lhs, const Value& rhs, CompOp op) {
    // 处理类型不匹配的情况（仅支持 INT 和 FLOAT 之间的转换比较）
    if (lhs.type != rhs.type) {
        if ((lhs.type == TYPE_INT && rhs.type == TYPE_FLOAT) ||
            (lhs.type == TYPE_FLOAT && rhs.type == TYPE_INT)) {
            // 转换为 double 进行比较（避免精度损失）
            double v1 = (lhs.type == TYPE_INT) ? lhs.int_val : lhs.float_val;
            double v2 = (rhs.type == TYPE_INT) ? rhs.int_val : rhs.float_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }
        // 其他类型不匹配的情况，直接返回 false
        return false;
    }

    // 类型相同的情况，根据类型进行比较
    switch (lhs.type) {
        case TYPE_INT: {
            int v1 = lhs.int_val;
            int v2 = rhs.int_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }

        case TYPE_FLOAT: {
            float v1 = lhs.float_val;
            float v2 = rhs.float_val;

            switch (op) {
                case OP_EQ: return v1 == v2;
                case OP_NE: return v1 != v2;
                case OP_LT: return v1 < v2;
                case OP_GT: return v1 > v2;
                case OP_LE: return v1 <= v2;
                case OP_GE: return v1 >= v2;
                default: return false;
            }
        }

        case TYPE_STRING: {
            const std::string& s1 = lhs.str_val;
            const std::string& s2 = rhs.str_val;

            switch (op) {
                case OP_EQ: return s1 == s2;
                case OP_NE: return s1 != s2;
                case OP_LT: return s1 < s2;
                case OP_GT: return s1 > s2;
                case OP_LE: return s1 <= s2;
                case OP_GE: return s1 >= s2;
                default: return false;
            }
        }

        default:
            return false;
    }
}

// 辅助函数：比较两个值
int AggregationExecutor::compare_values(const Value& v1, const Value& v2) {
    if (v1.type != v2.type) {
        // 类型不同时的处理
        if ((v1.type == TYPE_INT && v2.type == TYPE_FLOAT) || 
            (v1.type == TYPE_FLOAT && v2.type == TYPE_INT)) {
            double val1 = (v1.type == TYPE_INT) ? v1.int_val : v1.float_val;
            double val2 = (v2.type == TYPE_INT) ? v2.int_val : v2.float_val;
            
            if (val1 < val2) return -1;
            if (val1 > val2) return 1;
            return 0;
        }
        return 0; // 其他类型不匹配的情况
    }
    
    switch (v1.type) {
        case TYPE_INT:
            if (v1.int_val < v2.int_val) return -1;
            if (v1.int_val > v2.int_val) return 1;
            return 0;
        case TYPE_FLOAT:
            if (v1.float_val < v2.float_val) return -1;
            if (v1.float_val > v2.float_val) return 1;
            return 0;
        case TYPE_STRING:
            return v1.str_val.compare(v2.str_val);
        default:
            return 0;
    }
}


```

## (6)src/portal.h
1. 添加头文件
```c++
#include "execution/executor_aggregation.h"
```
2. 188行后添加
```c++
} else if(auto x = std::dynamic_pointer_cast<AggregationPlan>(plan)) {
            return std::make_unique<AggregationExecutor>(convert_plan_executor(x->subplan_, context),
                                                        x->group_cols_, x->agg_exprs_,
                                                        x->having_conds_, x->sel_cols_);
```

## (7)src/execution/CMakeLists.txt
1. 第1行修改为
```c++
set(SOURCES execution_manager.cpp executor_aggregation.cpp)
```


## (8)src/analyze/analyze.cpp
1. 149行后添加
```c++
// 验证聚合表达式中的列是否存在
            if (aggexpr->col != nullptr) {
                TabCol agg_col = {aggexpr->col->tab_name, aggexpr->col->col_name};
                agg_col = check_column(all_cols, agg_col);
                // 更新聚合表达式中的列信息
                aggexpr->col->tab_name = agg_col.tab_name;
                aggexpr->col->col_name = agg_col.col_name;
            }
            query->agg_exprs.push_back(*aggexpr);
```
2. 192行后添加
```c++
validate_aggregation_query(x, query, all_cols);
```
3. 356行后添加
```c++
/**
 * @brief 验证聚合查询的语义正确性
 *
 * @param stmt GroupByStmt AST节点
 * @param query 查询对象
 * @param all_cols 所有列的元数据
 */
void Analyze::validate_aggregation_query(std::shared_ptr<ast::GroupByStmt> stmt, std::shared_ptr<Query> query, const std::vector<ColMeta> &all_cols) {
    // 1. 检查WHERE子句中不能使用聚合函数
    validate_where_clause_no_aggregates(stmt->conds);

    // 2. 检查SELECT列表中的非聚合列必须出现在GROUP BY中
    validate_select_columns_in_group_by(stmt, all_cols);

    // 3. 检查聚合函数的参数类型
    validate_aggregate_function_types(stmt->agg_exprs, all_cols);

    // 4. 检查GROUP BY列的有效性
    validate_group_by_columns(stmt->group_cols, all_cols);
}

/**
 * @brief 检查WHERE子句中不能使用聚合函数
 */
void Analyze::validate_where_clause_no_aggregates(const std::vector<std::shared_ptr<ast::BinaryExpr>>& where_conds) {
    for (const auto& cond : where_conds) {
        // 检查左操作数
        if (auto agg_expr = std::dynamic_pointer_cast<ast::AggExpr>(cond->lhs)) {
            throw RMDBError("WHERE clause cannot contain aggregate functions");
        }

        // 检查右操作数
        if (auto agg_expr = std::dynamic_pointer_cast<ast::AggExpr>(cond->rhs)) {
            throw RMDBError("WHERE clause cannot contain aggregate functions");
        }
    }
}

/**
 * @brief 检查SELECT列表中的非聚合列必须出现在GROUP BY中
 */
void Analyze::validate_select_columns_in_group_by(std::shared_ptr<ast::GroupByStmt> stmt, const std::vector<ColMeta> &all_cols) {
    // 如果没有GROUP BY子句但有聚合函数，则SELECT中不能有非聚合列
    if (stmt->group_cols.empty() && !stmt->agg_exprs.empty()) {
        // 当有聚合函数但没有GROUP BY时，SELECT列表中不应该有普通列
        // 这里我们只检查是否有聚合函数，如果有，就允许查询继续
        return;
    }

    // 如果有GROUP BY子句，检查SELECT中的非聚合列是否都在GROUP BY中
    for (const auto& sel_col : stmt->select_cols) {
        bool found_in_group_by = false;

        for (const auto& group_col : stmt->group_cols) {
            if (sel_col->tab_name == group_col->tab_name && sel_col->col_name == group_col->col_name) {
                found_in_group_by = true;
                break;
            }
        }

        if (!found_in_group_by) {
            throw RMDBError("SELECT list contains non-aggregate column '" + sel_col->col_name + "' that is not in GROUP BY clause");
        }
    }
}

/**
 * @brief 检查聚合函数的参数类型
 */
void Analyze::validate_aggregate_function_types(const std::vector<std::shared_ptr<ast::AggExpr>>& agg_exprs, const std::vector<ColMeta> &all_cols) {
    for (const auto& agg_expr : agg_exprs) {
        if (agg_expr->col == nullptr) {
            // COUNT(*) 总是有效的
            if (agg_expr->type != AGG_COUNT) {
                throw RMDBError("Only COUNT can use * as parameter");
            }
            continue;
        }

        // 查找列的类型
        ColMeta col_meta;
        bool found = false;
        for (const auto& col : all_cols) {
            if (col.tab_name == agg_expr->col->tab_name && col.name == agg_expr->col->col_name) {
                col_meta = col;
                found = true;
                break;
            }
        }

        if (!found) {
            throw ColumnNotFoundError(agg_expr->col->col_name);
        }

        // 检查聚合函数与列类型的兼容性
        switch (agg_expr->type) {
            case AGG_COUNT:
                // COUNT可以用于任何类型
                break;
            case AGG_SUM:
            case AGG_AVG:
                // SUM和AVG只能用于数值类型
                if (col_meta.type != TYPE_INT && col_meta.type != TYPE_FLOAT) {
                    throw RMDBError("SUM/AVG can only be used with numeric columns");
                }
                break;
            case AGG_MAX:
            case AGG_MIN:
                // MAX和MIN只能用于数值类型
                if (col_meta.type != TYPE_INT && col_meta.type != TYPE_FLOAT) {
                    throw RMDBError("MAX/MIN can only be used with numeric columns");
                }
                break;
            default:
                throw RMDBError("Unknown aggregate function type");
        }
    }
}

/**
 * @brief 检查GROUP BY列的有效性
 */
void Analyze::validate_group_by_columns(const std::vector<std::shared_ptr<ast::Col>>& group_cols, const std::vector<ColMeta> &all_cols) {
    for (const auto& group_col : group_cols) {
        bool found = false;
        for (const auto& col : all_cols) {
            if (col.tab_name == group_col->tab_name && col.name == group_col->col_name) {
                found = true;
                break;
            }
        }

        if (!found) {
            throw ColumnNotFoundError(group_col->col_name);
        }
    }
}

```

## (9)src/analyze/analyze.h
1. 63行后添加
```c++
void validate_aggregation_query(std::shared_ptr<ast::GroupByStmt> stmt, std::shared_ptr<Query> query, const std::vector<ColMeta> &all_cols);
    void validate_where_clause_no_aggregates(const std::vector<std::shared_ptr<ast::BinaryExpr>>& where_conds);
    void validate_select_columns_in_group_by(std::shared_ptr<ast::GroupByStmt> stmt, const std::vector<ColMeta> &all_cols);
    void validate_aggregate_function_types(const std::vector<std::shared_ptr<ast::AggExpr>>& agg_exprs, const std::vector<ColMeta> &all_cols);
    void validate_group_by_columns(const std::vector<std::shared_ptr<ast::Col>>& group_cols, const std::vector<ColMeta> &all_cols);
```
## (10)src/parser/yacc.y
1. 修改489行
```c++
 /* epsilon */ { $$ = ""; }
```
2. 171行删除并添加：
```c++
// 如果聚合函数没有别名，生成默认列名
                std::string col_name = item->agg_expr->alias;
                if (col_name.empty()) {
                    // 生成默认列名，如 MAX(id), COUNT(*) 等
                    std::string func_name;
                    switch (item->agg_expr->type) {
                        case AGG_COUNT: func_name = "COUNT"; break;
                        case AGG_SUM: func_name = "SUM"; break;
                        case AGG_AVG: func_name = "AVG"; break;
                        case AGG_MAX: func_name = "MAX"; break;
                        case AGG_MIN: func_name = "MIN"; break;
                        default: func_name = "UNKNOWN"; break;
                    }

                    if (item->agg_expr->col) {
                        col_name = func_name + "(" + item->agg_expr->col->col_name + ")";
                    } else {
                        col_name = func_name + "(*)";
                    }
                    item->agg_expr->alias = col_name;  // 更新别名
                }

                std::shared_ptr<Col> col = std::make_shared<Col>("", col_name);
```
# (11)common.h
- 修改value结构体
```c++
struct Value {
    ColType type;  // type of value
    union {
        int int_val;      // int value
        float float_val;  // float value
    };
    std::string str_val;  // string value
    bool is_null;         // NULL value flag

    std::shared_ptr<RmRecord> raw;  // raw record buffer

    // 默认构造函数
    Value() : type(TYPE_INT), int_val(0), is_null(false) {}

    void set_int(int int_val_) {
        type = TYPE_INT;
        int_val = int_val_;
        is_null = false;
    }

    void set_float(float float_val_) {
        type = TYPE_FLOAT;
        float_val = float_val_;
        is_null = false;
    }

    void set_str(std::string str_val_) {
        type = TYPE_STRING;
        str_val = std::move(str_val_);
        is_null = false;
    }

    void set_null() {
        is_null = true;
        // 保持type不变，但值无意义
    }
```