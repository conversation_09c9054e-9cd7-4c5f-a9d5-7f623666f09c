-- 综合聚合函数测试
create table students (id int, name char(10), age int, score float);
insert into students values(1, '<PERSON>', 20, 95.5);
insert into students values(2, '<PERSON>', 21, 87.0);
insert into students values(3, '<PERSON>', 20, 92.5);
insert into students values(4, '<PERSON>', 21, 88.5);
insert into students values(5, 'Eve', 20, 96.0);

-- 测试1: 所有聚合函数类型
select COUNT(*) as total_count from students;
select COUNT(score) as score_count from students;
select MAX(score) as max_score from students;
select MIN(score) as min_score from students;
select SUM(score) as total_score from students;
select AVG(score) as avg_score from students;

-- 测试2: GROUP BY分组统计
select age, COUNT(*) as count, MAX(score) as max_score, MIN(score) as min_score, AVG(score) as avg_score from students group by age;

-- 测试3: HAVING条件过滤
select age, COUNT(*) as count, AVG(score) as avg_score from students group by age having COUNT(*) >= 2;

-- 测试4: ORDER BY排序（如果支持）
select age, COUNT(*) as count, AVG(score) as avg_score from students group by age order by avg_score desc;

-- 测试5: 复杂HAVING条件
select age, COUNT(*) as count, AVG(score) as avg_score from students group by age having AVG(score) > 90.0;

drop table students;
