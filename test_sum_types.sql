create table grade(id int,score float);
insert into grade values(1,88);
insert into grade values(2,89);
insert into grade values(3,90);
insert into grade values(4,91);
insert into grade values(5,92);

-- 测试SUM(int列) - 应该返回int类型
select SUM(id) as sum_id from grade;

-- 测试SUM(float列) - 应该返回float类型
select SUM(score) as sum_score from grade;

-- 测试AVG - 应该始终返回float类型
select AVG(id) as avg_id from grade;
select AVG(score) as avg_score from grade;

drop table grade;
