create table test(id int,value float);

-- COUNT函数应该返回0（一行数据）
select COUNT(*) from test;
select COUNT(value) from test;
select COUNT(id) from test;

-- 其他聚合函数应该只显示表头（0行数据）
select MAX(value) from test;
select MIN(value) from test;
select SUM(value) from test;
select AVG(value) from test;

-- 混合聚合查询（有COUNT）应该返回一行
select COUNT(*), MAX(value) from test;

-- 混合聚合查询（无COUNT）应该返回0行
select MAX(value), MIN(value) from test;

drop table test;
