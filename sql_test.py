import pexpect
import sys


def batch_execute_sql(sql_file_path, client_path):
    """
    批量执行SQL语句到rmdb_client
    :param sql_file_path: SQL脚本文件路径（每行一条SQL，以分号结尾）
    :param client_path: rmdb_client可执行文件路径
    """
    # 1. 读取SQL脚本
    with open(sql_file_path, 'r') as f:
        sql_commands = [line.strip() for line in f if line.strip()]  # 过滤空行

    # 2. 启动客户端进程
    child = pexpect.spawn(client_path, encoding='utf-8')
    child.logfile = sys.stdout  # 实时打印客户端输出（可选，方便调试）

    try:
        # 3. 等待初始提示符
        child.expect('Rucbase> ', timeout=5)

        # 4. 逐行执行SQL
        for sql in sql_commands:
            child.sendline(sql)  # 发送SQL命令
            child.expect('Rucbase> ', timeout=10)  # 等待下一个提示符（调整timeout适应慢查询）

    except pexpect.TIMEOUT:
        print("超时错误：客户端无响应或SQL执行过慢！")
    except pexpect.EOF:
        print("客户端意外退出！")
    finally:
        child.close()


if __name__ == "__main__":
    # 配置参数
    SQL_FILE = "batch_sql.sql"  # 替换为你的SQL脚本路径
    CLIENT_EXEC = "./rmdb_client/build/rmdb_client"  # 替换为实际可执行文件路径

    batch_execute_sql(SQL_FILE, CLIENT_EXEC)