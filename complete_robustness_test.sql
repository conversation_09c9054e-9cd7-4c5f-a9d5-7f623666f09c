create table test (id int, name char(10), score float);
insert into test values(1, '<PERSON>', 95.0);
insert into test values(1, '<PERSON>', 87.0);
insert into test values(2, '<PERSON>', 92.0);

-- 测试1: SELECT中非聚合列不在GROUP BY中（应该报错）
select id, name, MAX(score) from test group by id;

-- 测试2: WHERE子句中使用聚合函数（应该报错）
select id, MAX(score) from test where COUNT(*) > 1 group by id;

-- 测试3: 正确的聚合查询（应该成功）
select id, MAX(score) as max_score from test group by id;

-- 测试4: 正确的HAVING查询（应该成功）
select id, MAX(score) as max_score from test group by id having COUNT(*) > 1;

drop table test;
